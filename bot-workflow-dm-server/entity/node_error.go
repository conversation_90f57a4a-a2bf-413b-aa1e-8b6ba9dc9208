package entity

import (
	"errors"
	"fmt"
)

// NodeErr 节点错误信息
type NodeErr struct {
	Code string
	Msg  string
}

// Error 实现了 error 接口并返回错误描述
func (e NodeErr) Error() string {
	if e.Code == "" && e.Msg == "" {
		return ""
	}
	return fmt.Sprintf("code:%s, msg:%s", e.Code, e.Msg)
}

// GetNodeErrCode 从error中获取错误码
func GetNodeErrCode(e error) string {
	if e == nil {
		return ""
	}
	err, ok := e.(*NodeErr)
	if !ok && !errors.As(e, &err) {
		return ErrUnknown.Code
	}
	if err == nil {
		return ""
	}
	return err.Code
}

// GetNodeErrMsg 从error中获取错误信息
func GetNodeErrMsg(e error) string {
	if e == nil {
		return ""
	}
	err, ok := e.(*NodeErr)
	if !ok && !errors.As(e, &err) {
		return GetNodeErrMsgf(e, e.Error())
	}
	if err == nil {
		return ""
	}
	return GetNodeErrMsgf(e)
}

// GetNodeErrMsgf 从error中获取错误信息并补充额外内容
func GetNodeErrMsgf(e error, extraErrMsgs ...any) string {
	if e == nil {
		return ""
	}
	errMsg := ErrUnknown.Msg
	err, ok := e.(*NodeErr)
	if ok || errors.As(e, &err) {
		if err != nil {
			errMsg = err.Msg
		}
	}
	if len(extraErrMsgs) > 0 {
		extra := ""
		for i, errMsg := range extraErrMsgs {
			extra += fmt.Sprintf("%v", errMsg)
			if i != len(extraErrMsgs)-1 {
				extra += "; "
			}
		}
		return errMsg + ": " + extra
	}
	return errMsg
}

// WrapNodeErr 在已有 NodeErr 的基础上补充额外信息，并返回一个新的 NodeErr 指针
func WrapNodeErr(ne *NodeErr, extraErrMsgs ...any) error {
	return &NodeErr{
		Code: ne.Code,
		Msg:  GetNodeErrMsgf(ne, extraErrMsgs...),
	}
}

// 节点运行错误码（格式参考云API的二级错误码），需要在errMsgMap中补充基础错误信息
var (
	// ErrUnknown 未知错误
	ErrUnknown = &NodeErr{Code: "NodeErr.Unknown", Msg: "未知错误"}
	// ErrSystemError 系统内部错误（数据不完整、数据错误、数据不一致、组件异常、服务异常等）
	ErrSystemError = &NodeErr{Code: "NodeErr.SystemError", Msg: "系统错误"}
	// ErrMissingParam 获取输入变量失败
	ErrMissingParam = &NodeErr{Code: "NodeErr.MissingParam", Msg: "获取输入变量失败"}
	// ErrInvalidParam 参数解析失败
	ErrInvalidParam = &NodeErr{Code: "NodeErr.InvalidParam", Msg: "参数解析失败"}
	// ErrParseReference 参数引用解析失败
	ErrParseReference = &NodeErr{Code: "NodeErr.ParseReference", Msg: "参数引用解析失败"}
	// ErrInvalidParamValue 参数值不合法(数值有误、数值的类型不对等)
	ErrInvalidParamValue = &NodeErr{Code: "NodeErr.InvalidParamValue", Msg: "参数值不合法，请检查参数值是否正确"}
	// ErrRuntimeError 运行时错误
	ErrRuntimeError = &NodeErr{Code: "NodeErr.RuntimeError", Msg: "运行时错误"}
	// ErrRuntimeWorkflowTerminated 工作流已运行结束
	ErrRuntimeWorkflowTerminated = &NodeErr{Code: "NodeErr.RuntimeWorkflowTerminated", Msg: "工作流已运行结束"}

	// ErrLLMNoBalance 大模型资源不足
	ErrLLMNoBalance = &NodeErr{Code: "NodeErr.LLMNoBalance", Msg: "大模型资源不足"}
	// ErrLLMOverload 大模型并发超过限制
	ErrLLMOverload = &NodeErr{Code: "NodeErr.LLMOverload", Msg: "大模型并发数超出限制"}
	// ErrLLMTimeout 大模型请求超时
	ErrLLMTimeout = &NodeErr{Code: "NodeErr.LLMTimeout", Msg: "大模型请求超时"}
	// ErrLLMFail 大模型请求失败（长度超过限制、服务异常等）
	ErrLLMFail = &NodeErr{Code: "NodeErr.LLMFail", Msg: "大模型请求失败"}
	// ErrLLMResultError 大模型结果异常（内容空、非Json、缺少字段、值的类型不对等）
	ErrLLMResultError = &NodeErr{Code: "NodeErr.LLMResultError", Msg: "大模型结果异常"}
	// ErrLLMLogicEvaluate 大模型逻辑判断失败
	ErrLLMLogicEvaluate = &NodeErr{Code: "NodeErr.LLMLogicEvaluate", Msg: "大模型逻辑判断失败"}
	// ErrLLMPromptTooLong 大模型请求的prompt超长
	ErrLLMPromptTooLong = &NodeErr{Code: "NodeErr.LLMPromptTooLong", Msg: "大模型请求的prompt超长"}
	// ErrLLMProbeExceedMaxLimit 大模型追问次数超过限制
	ErrLLMProbeExceedMaxLimit = &NodeErr{Code: "NodeErr.LLMProbeExceedMaxLimit", Msg: "大模型追问次数超过限制"}

	// ErrSetSubWorkflowInputFailed 【循环节点】设置子工作流输入参数失败
	ErrSetSubWorkflowInputFailed = &NodeErr{Code: "NodeErr.SetSubWorkflowInputFailed", Msg: "设置子工作流输入参数失败"}
	// ErrRunSubWorkflowNodeFailed 子工作流的节点运行异常
	ErrRunSubWorkflowNodeFailed = &NodeErr{Code: "NodeErr.RunSubWorkflowNodeFailed", Msg: "子工作流的节点运行异常"}
	// ErrInitSubWorkflowFailed 初始化子工作流失败
	ErrInitSubWorkflowFailed = &NodeErr{Code: "NodeErr.InitSubWorkflowFailed", Msg: "初始化子工作流失败"}

	// ErrExecuteCodeFailed 【代码节点】代码执行失败（代码有问题、超时、没运行资源、资源超过限制）
	ErrExecuteCodeFailed = &NodeErr{Code: "NodeErr.ExecuteCodeFailed", Msg: "代码执行失败"}
	// ErrJudgeIntentFailed 【意图判断节点】意图判断结果异常
	ErrJudgeIntentFailed = &NodeErr{Code: "NodeErr.JudgeIntentFailed", Msg: "意图判断结果异常"}
	// ErrIterationJudgeFailed 【循环节点】循环条件判断失败
	ErrIterationJudgeFailed = &NodeErr{Code: "NodeErr.IterationJudgeFailed", Msg: "循环条件判断失败"}
	// ErrExceedIterationMaxLimit 【循环节点】超过系统最大循环次数
	ErrExceedIterationMaxLimit = &NodeErr{Code: "NodeErr.ExceedIterationMaxLimit", Msg: "循环超过系统最大循环次数"}
	// ErrSearchKnowledge 【知识检索节点】知识库检索失败
	ErrSearchKnowledge = &NodeErr{Code: "NodeErr.SearchKnowledge", Msg: "知识库检索失败"}
	// ErrExistUnsupportOperator 【逻辑判断节点】存在不支持的比较符
	ErrExistUnsupportOperator = &NodeErr{Code: "NodeErr.ExistUnsupportOperator", Msg: "存在不支持的比较符"}
	// ErrJudgeLogicalFailed 【逻辑判断节点】没有满足条件的分支
	ErrJudgeLogicalFailed = &NodeErr{Code: "NodeErr.JudgeLogicalFailed", Msg: "逻辑判断异常，没有满足条件的分支"}
	// ErrGetPluginToolInfoFailed 【插件节点】获取工具详情失败
	ErrGetPluginToolInfoFailed = &NodeErr{Code: "NodeErr.GetPluginToolInfoFailed", Msg: "获取插件工具详情失败"}
	// ErrCallPluginToolFailed 【插件节点】调用工具异常（建立连接失败、连接超时）
	ErrCallPluginToolFailed = &NodeErr{Code: "NodeErr.CallPluginToolFailed", Msg: "调用工具异常"}
	// ErrPluginToolResultError 【插件节点】工具结果异常
	ErrPluginToolResultError = &NodeErr{Code: "NodeErr.PluginToolResultError", Msg: "工具结果异常"}
	// ErrCallAPIFail 【工具节点】调用API失败
	ErrCallAPIFail = &NodeErr{Code: "NodeErr.CallAPIFail", Msg: "调用API失败"}
	// ErrConnectFailed 【消息队列节点】建立连接失败
	ErrConnectFailed = &NodeErr{Code: "NodeErr.ConnectFailed", Msg: "建立连接失败"}
	// ErrMQTypeUnsupported 【消息队列节点】消息队列类型不支持
	ErrMQTypeUnsupported = &NodeErr{Code: "NodeErr.MQTypeUnsupported", Msg: "消息队列类型不支持"}
	// ErrMQInitProducerFailed 【消息队列节点】初始化消息队列生产者失败
	ErrMQInitProducerFailed = &NodeErr{Code: "NodeErr.MQInitProducerFailed", Msg: "初始化消息队列生产者失败"}
	// ErrMQSendMessageFailed 【消息队列节点】消息队列生产者发送消息失败
	ErrMQSendMessageFailed = &NodeErr{Code: "NodeErr.MQSendMessageFailed", Msg: "消息队列生产者发送消息失败"}
	// ErrInvalidBatchSize 【批处理节点】批处理并行数不合法
	ErrInvalidBatchSize = &NodeErr{Code: "NodeErr.InvalidBatchSize", Msg: "批处理并行数不合法"}
)
