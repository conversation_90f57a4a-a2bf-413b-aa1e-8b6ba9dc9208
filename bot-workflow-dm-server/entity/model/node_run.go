package model

import "time"

// NodeRunState 节点运行状态
type NodeRunState string

const (
	// NodeRunStateInit 初始化
	NodeRunStateInit NodeRunState = "INIT"
	// NodeRunStateRunning 运行中
	NodeRunStateRunning NodeRunState = "RUNNING"
	// NodeRunStateSuccess 成功
	NodeRunStateSuccess NodeRunState = "SUCCESS"
	// NodeRunStateFailed 失败
	NodeRunStateFailed NodeRunState = "FAILED"
	// NodeRunStateCanceled 已取消
	NodeRunStateCanceled NodeRunState = "CANCELED"
	// NodeRunStateWaitingInput 等待输入
	NodeRunStateWaitingInput NodeRunState = "WAITING_INPUT"
)

// NodeRun 工作流运行节点记录（数据库模型）
type NodeRun struct {
	ID               int64        `gorm:"column:f_id;primaryKey;autoIncrement"`
	WorkflowRunID    string       `gorm:"column:f_workflow_run_id;index:idx_workflow_run_id"`
	NodeRunID        string       `gorm:"column:f_node_run_id;uniqueIndex:uk_node_run_id"`
	BelongNodeID     string       `gorm:"column:f_belong_node_id;index:idx_belong_node_id"`
	WorkflowID       string       `gorm:"column:f_workflow_id"`
	NodeID           string       `gorm:"column:f_node_id"`
	NodeName         string       `gorm:"column:f_node_name"`
	NodeType         string       `gorm:"column:f_node_type"`
	State            NodeRunState `gorm:"column:f_state;type:enum('INIT','RUNNING','SUCCESS','FAILED','CANCELED');default:INIT"`
	FailMessage      string       `gorm:"column:f_fail_message"`
	Input            string       `gorm:"column:f_input;type:mediumtext"`
	Output           string       `gorm:"column:f_output;type:mediumtext"`
	TaskOutput       string       `gorm:"column:f_task_output;type:mediumtext"`
	CostMilliseconds uint32       `gorm:"column:f_cost_milliseconds"`
	StatisticInfos   string       `gorm:"column:f_statistic_infos"`
	StartTime        *time.Time   `gorm:"column:f_start_time"`
	EndTime          *time.Time   `gorm:"column:f_end_time"`
	CreateTime       time.Time    `gorm:"column:f_create_time"`
	UpdateTime       time.Time    `gorm:"column:f_update_time"`
}

// TableName 指定表名
func (NodeRun) TableName() string {
	return "t_node_run"
}
