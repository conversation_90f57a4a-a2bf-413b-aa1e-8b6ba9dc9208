package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"time"

	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/entity"
	"git.woa.com/dialogue-platform/bot-dm/bot-workflow-dm-server/util"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF"
	"git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"
)

// BatchNodeOutput 并行节点的结果结构
type BatchNodeOutput struct {
	Results []map[string]any // 并行的结果
}

func executeBatchNode(ctx context.Context, session *entity.Session, nodeTask *entity.NodeTask,
	nodeResultQueue chan entity.NodeResult) {
	node := nodeTask.Node
	LogWorkflow(ctx).Infof("executeBatchNode start, nodeName: %v, nodeID: %v", node.NodeName, node.NodeID)
	workflowRun := session.GetCurWorkflowRun()
	nodeRun := workflowRun.GetNodeRun(nodeTask.BelongNodeID, node.NodeID)

	// 获取节点输入，err需要使用entity中的NodeErr，避免错误码、错误信息为空
	inputResult, _, err := getInputResult(session, nodeTask.BelongNodeID, node.GetInputs())
	if err != nil {
		LogWorkflow(ctx).Errorf("executeBatchNode error: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, err)
		return
	}

	nodeData := node.GetBatchNodeData()
	if nodeData == nil {
		LogWorkflow(ctx).Errorf("invalid node data")
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrMissingParam)
		return
	}

	// 获取当前的并行信息，err需要使用entity中的NodeErr，避免错误码、错误信息为空
	parallelInfo, statisticInfos, err := getParallelInfo(ctx, session, nodeTask.BelongNodeID, node)
	if err != nil {
		LogWorkflow(ctx).Errorf("getParallelInfo failed, error: %v", err)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, err)
		return
	}
	nodeRunOutput := updateParallelNodeOutput(parallelInfo)

	// 计算并行的总数量 并 更新并行信息
	loopArrayName := nodeData.GetSpecifiedTraversalVariable()
	if loopArrayName == "" {
		loopArrayName = loopOldArrayName
	}
	totalParallelNum := getLength(inputResult[loopArrayName])
	parallelInfo.MaxIndex = totalParallelNum
	// 计算剩余并行数量
	indexes := getSortedParallelIndexes(parallelInfo)
	needParallelNum := totalParallelNum - len(indexes)
	// 计算当前需要启动的并行数量 并 更新并行信息
	parallelNum := int(nodeData.GetMaxParallel())
	if parallelNum <= 0 {
		LogWorkflow(ctx).Errorf("executeBatchNode size invalid: %d", parallelNum)
		sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrInvalidBatchSize)
		return
	}
	if parallelNum > needParallelNum {
		parallelNum = needParallelNum
	}
	currentParallelIndex := parallelInfo.Index
	latestParallelIndex := len(indexes) + parallelNum
	parallelInfo.Index = latestParallelIndex

	// 启动子工作流
	for i := currentParallelIndex + 1; i <= latestParallelIndex; i++ {
		// 更新inputResult，考虑入参可能会是数组的Item
		expandParallelInputResult(inputResult, i)
		// 初始化子工作流
		startNodeInputResult, err := parseInputParams(session, nodeTask.BelongNodeID, nodeData.GetRefInputs(), inputResult)
		if err != nil {
			LogWorkflow(ctx).Errorf("parseInputParams failed, error: %v", err)
			sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrSetSubWorkflowInputFailed,
				err.Error())
			return
		}
		belongNodeID := genBelongID(nodeTask.BelongNodeID, node.NodeID, i)
		if err = initSubWorkflow(session, node, startNodeInputResult, belongNodeID); err != nil {
			LogWorkflow(ctx).Errorf("initSubWorkflow failed, error: %v", err)
			sendFailedResult(nodeResultQueue, nodeTask.BelongNodeID, node.NodeID, entity.ErrInitSubWorkflowFailed, err.Error())
			return
		}
		parallelInfo.SetRunningIndex(i)
	}

	// 计算节点状态
	nodeRunStatus := entity.NodeStatusRunning
	if len(parallelInfo.GetCompleteIndexes()) == totalParallelNum {
		nodeRunStatus = entity.NodeStatusSuccess
	}

	// 构造节点结果
	batchNodeResult := entity.NodeResult{
		BelongNodeID:            nodeTask.BelongNodeID,
		NodeID:                  node.NodeID,
		Status:                  nodeRunStatus,
		Input:                   util.ToJsonString(inputResult),
		Output:                  util.ToJsonString(nodeRunOutput),
		SubWorkflowCompleteOnce: false,
		ParallelInfo:            parallelInfo,
		StatisticInfo:           mergeStatisticInfos(statisticInfos),
		CostMilliSeconds:        time.Since(nodeRun.StartTime).Milliseconds(),
	}

	nodeResultQueue <- batchNodeResult
	LogWorkflow(ctx).Infof("executeBatchNode done, nodeName: %v", node.NodeName)
}

func getParallelInfo(ctx context.Context, session *entity.Session, belongID string,
	node *KEP_WF.WorkflowNode) (*entity.ParallelInfo, []*KEP_WF_DM.StatisticInfo, error) {
	workflowRun := session.GetCurWorkflowRun()
	nodeRun := workflowRun.GetNodeRun(belongID, node.NodeID)
	parallelInfo := nodeRun.ParallelInfo
	firstRound := false
	newParallelInfo := entity.NewParallelInfo()
	// 首次
	if parallelInfo == nil {
		firstRound = true
	}
	if firstRound {
		nodeRun.ParallelInfo = newParallelInfo
		return newParallelInfo, nil, nil
	}
	newParallelInfo = parallelInfo

	// 非首轮，每执行完一轮，更新Index、Code、Log、Output等
	subWorkflow := session.GetWorkflow(node.GetBatchNodeData().GetWorkflowID())
	indexes := getSortedParallelIndexes(parallelInfo)
	var totalStatisticInfos []*KEP_WF_DM.StatisticInfo
	for _, index := range indexes {
		belongNodeID := genBelongID(belongID, node.NodeID, index)
		newParallelInfo.SetErrorCode(index, "")
		newParallelInfo.SetLog(index, "")
		isCompleted := false
		var statisticInfos []*KEP_WF_DM.StatisticInfo
		for _, subNode := range subWorkflow.Nodes {
			subNodeRun := workflowRun.GetNodeRun(belongNodeID, subNode.NodeID)
			if subNodeRun != nil {
				if subNodeRun.Status == entity.NodeStatusFailed {
					isCompleted = true
					newParallelInfo.SetErrorCode(index, subNodeRun.ErrorCode)
					newParallelInfo.SetLog(index, entity.GetNodeErrMsgf(entity.ErrRunSubWorkflowNodeFailed,
						subNodeRun.FailMessage))
				}
				statisticInfos = append(statisticInfos, subNodeRun.StatisticInfo...)
			}
			if subNode.NodeType == KEP_WF.NodeType_END {
				if subNodeRun.IsFinished() {
					isCompleted = true
				}
				if subNodeRun != nil && subNodeRun.Status == entity.NodeStatusSuccess {
					subOutput := make(map[string]any)
					err := json.Unmarshal([]byte(subNodeRun.Output), &subOutput)
					if err != nil {
						LogWorkflow(ctx).Errorf("unmarshal subEndNodeRun.Output failed, error: %v, Output: %v",
							err, subNodeRun.Output)
						return nil, totalStatisticInfos, entity.WrapNodeErr(entity.ErrInvalidParam, err.Error())
					}
					newParallelInfo.SetOutput(index, subOutput)
				}
			}
		}
		if isCompleted {
			// 本次工作流完成后才需要统计大模型token用量信息
			totalStatisticInfos = append(totalStatisticInfos, statisticInfos...)
			if parallelInfo.IsIndexRunning(index) && parallelInfo.IsIndexCompleted(index) {
				newParallelInfo.SetCompleteIndex(index)
				newParallelInfo.DeleteRunningIndex(index)
			}
		} else {
			newParallelInfo.SetRunningIndex(index)
		}
	}
	nodeRun.ParallelInfo = newParallelInfo
	LogWorkflow(ctx).Debugf("getParallelInfo, nodeName: %v, nodeID: %v,old: %v, new: %v",
		node.NodeName, node.NodeID, util.ToJsonString(parallelInfo), util.ToJsonString(newParallelInfo))

	return nodeRun.ParallelInfo, totalStatisticInfos, nil
}

func expandParallelInputResult(inputResult map[string]any, index int) {
	// 扩充数组的Item的值
	addInputResult := make(map[string]any)
	for inputKey, inputValue := range inputResult {
		inputArray := reflect.ValueOf(inputValue)
		if !util.IsArray(inputArray) {
			continue
		}
		itemKey := fmt.Sprintf("%v.%v", inputKey, loopItemName)
		if inputArray.Len() >= index {
			addInputResult[itemKey] = inputArray.Index(index - 1).Interface()
		} else {
			addInputResult[itemKey] = nil
		}
	}
	for inputKey, inputValue := range addInputResult {
		inputResult[inputKey] = inputValue
	}
}

func getSortedParallelIndexes(parallelInfo *entity.ParallelInfo) []int {
	indexSet := make(map[int]struct{})
	for index := range parallelInfo.GetRunningIndexes() {
		indexSet[index] = struct{}{}
	}
	for index := range parallelInfo.GetCompleteIndexes() {
		indexSet[index] = struct{}{}
	}
	indexes := make([]int, 0)
	for index := range indexSet {
		indexes = append(indexes, index)
	}
	sort.Ints(indexes)
	return indexes
}

func updateParallelNodeOutput(parallelInfo *entity.ParallelInfo) any {
	nodeRunOutput := &BatchNodeOutput{}
	for i := range parallelInfo.GetCompleteIndexes() {
		results := parallelInfo.GetOutput(i)
		if results == nil {
			results = make(map[string]any)
		}
		// 如果错误码不为空，说明批处理执行异常，需要输出错误信息到结果里
		if errCode := parallelInfo.GetErrorCode(i); errCode != "" {
			results[entity.ErrorField] = map[string]any{
				entity.ErrorCodeField:    errCode,
				entity.ErrorMessageField: parallelInfo.GetLog(i),
			}
		}
		nodeRunOutput.Results = append(nodeRunOutput.Results, results)
	}
	return nodeRunOutput
}
