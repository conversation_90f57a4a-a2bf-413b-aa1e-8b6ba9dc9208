package service

import "git.woa.com/dialogue-platform/lke_proto/pb-protocol/KEP_WF_DM"

var (
	loopNodeBaseReqs = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs:      map[string]string{"Input": `["1","2"]`},
			NodeJSON: `{
				  "NodeID": "f5049ac9-c045-cfad-db21-32414782676b",
				  "NodeName": "循环1",
				  "NodeType": "ITERATION",
				  "NodeDesc": "",
				  "Inputs": [
					{
					  "Name": "Input",
					  "Type": "ARRAY_STRING",
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "5fffece3-f10b-4517-aed7-75383ef35b49"
					  },
					  "Desc": "循环数组变量",
					  "nameTip": "该变量仅支持array<string>和array<object>类型",
					  "controlShowType": [
						"ARRAY_OBJECT",
						"ARRAY_STRING"
					  ],
					  "SubInputs": []
					},
					{
					  "Name": "arr_obj",
					  "Type": "ARRAY_OBJECT",
					  "Desc": "",
					  "IsRequired": false,
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "ec150ea1-96c5-4214-8318-d3c77069a477"
					  },
					  "SubInputs": []
					}
				  ],
				  "IterationNodeData": {
					"BodyType": "WORKFLOW",
						"WorkflowID": "a64b5b68-251d-40b0-92e4-e00924f0ffe9",
						"RefInputs": [
						  {
							"Name": "input_str",
							"Type": "STRING",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Input.Item"
							}
						  },
						  {
							"Name": "input_int",
							"Type": "INT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Index"
							}
						  },
						  {
							"Name": "input_obj",
							"Type": "OBJECT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Output"
							}
						  },
						  {
							"Name": "input_arr_obj",
							"Type": "ARRAY_OBJECT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "arr_obj"
						}
					  }
					],
					"IterationMode": "ALL",
					"Condition": {
					  "LogicalOperator": "UNSPECIFIED",
					  "Comparison": {
						"Left": {
							  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"LeftType": "STRING",
						"Operator": "UNSPECIFIED",
						"Right": {
						  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"MatchType": "SEMANTIC"
							}
						  }
					  }
					}`,
		},
	}

	loopNodeOptionCardReqs = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs:      map[string]string{"Input": `["1","2"]`},
			NodeJSON: `{
				  "NodeID": "f5049ac9-c045-cfad-db21-32414782676b",
				  "NodeName": "循环1",
				  "NodeType": "ITERATION",
				  "NodeDesc": "",
				  "Inputs": [
					{
					  "Name": "Input",
					  "Type": "ARRAY_STRING",
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "5fffece3-f10b-4517-aed7-75383ef35b49"
					  },
					  "Desc": "循环数组变量",
					  "nameTip": "该变量仅支持array<string>和array<object>类型",
					  "controlShowType": [
						"ARRAY_OBJECT",
						"ARRAY_STRING"
					  ],
					  "SubInputs": []
					},
					{
					  "Name": "arr_obj",
					  "Type": "ARRAY_OBJECT",
					  "Desc": "",
					  "IsRequired": false,
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "ec150ea1-96c5-4214-8318-d3c77069a477"
					  },
					  "SubInputs": []
					}
				  ],
				  "IterationNodeData": {
					"BodyType": "WORKFLOW",
					"WorkflowID": "option_card_workflow_id",
					"RefInputs": [
					  {
						"Name": "input_str",
						"Type": "STRING",
						"Desc": "",
						"IsRequired": false,
						"SubInputs": [],
						"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Input.Item"
						}
					  },
					  {
						"Name": "input_int",
						"Type": "INT",
						"Desc": "",
						"IsRequired": false,
						"SubInputs": [],
						"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Index"
						}
					  },
					  {
						"Name": "input_obj",
						"Type": "OBJECT",
						"Desc": "",
						"IsRequired": false,
						"SubInputs": [],
						"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Output"
						}
					  },
					  {
						"Name": "input_arr_obj",
						"Type": "ARRAY_OBJECT",
						"Desc": "",
						"IsRequired": false,
						"SubInputs": [],
						"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "arr_obj"
						}
					  }
					],
					"IterationMode": "ALL",
					"Condition": {
					  "LogicalOperator": "UNSPECIFIED",
					  "Comparison": {
						"Left": {
						  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"LeftType": "STRING",
						"Operator": "UNSPECIFIED",
						"Right": {
						  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"MatchType": "SEMANTIC"
					  }
					}
				  }
				}`,
			MainModelName: "test-model",
		},
		{
			SessionID:    "test-session",
			AppID:        AppID,
			RequestType:  KEP_WF_DM.RequestType_RUN,
			Inputs:       map[string]string{"Input": `["1","2"]`},
			Query:        "aaa",
			QueryHistory: []*KEP_WF_DM.Message{{Role: KEP_WF_DM.Role_ASSISTANT, Content: "请选择：：："}},
			NodeJSON: `{
				  "NodeID": "f5049ac9-c045-cfad-db21-32414782676b",
				  "NodeName": "循环1",
				  "NodeType": "ITERATION",
				  "NodeDesc": "",
				  "Inputs": [
					{
					  "Name": "Input",
					  "Type": "ARRAY_STRING",
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "5fffece3-f10b-4517-aed7-75383ef35b49"
					  },
					  "Desc": "循环数组变量",
					  "nameTip": "该变量仅支持array<string>和array<object>类型",
					  "controlShowType": [
						"ARRAY_OBJECT",
						"ARRAY_STRING"
					  ],
					  "SubInputs": []
					},
					{
					  "Name": "arr_obj",
					  "Type": "ARRAY_OBJECT",
					  "Desc": "",
					  "IsRequired": false,
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "ec150ea1-96c5-4214-8318-d3c77069a477"
					  },
					  "SubInputs": []
					}
				  ],
				  "IterationNodeData": {
					"BodyType": "WORKFLOW",
					"WorkflowID": "option_card_workflow_id",
					"RefInputs": [
					  {
						"Name": "input_str",
						"Type": "STRING",
						"Desc": "",
						"IsRequired": false,
						"SubInputs": [],
						"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Input.Item"
						}
					  },
					  {
						"Name": "input_int",
						"Type": "INT",
						"Desc": "",
						"IsRequired": false,
						"SubInputs": [],
						"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Index"
						}
					  },
					  {
						"Name": "input_obj",
						"Type": "OBJECT",
						"Desc": "",
						"IsRequired": false,
						"SubInputs": [],
						"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Output"
						}
					  },
					  {
						"Name": "input_arr_obj",
						"Type": "ARRAY_OBJECT",
						"Desc": "",
						"IsRequired": false,
						"SubInputs": [],
						"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "arr_obj"
						}
					  }
					],
					"IterationMode": "ALL",
					"Condition": {
					  "LogicalOperator": "UNSPECIFIED",
					  "Comparison": {
						"Left": {
						  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"LeftType": "STRING",
						"Operator": "UNSPECIFIED",
						"Right": {
						  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"MatchType": "SEMANTIC"
					  }
					}
				  }
				}`,
			MainModelName: "test-model",
		},
	}
	workflowNodeReqs = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs:      map[string]string{"Input": `["1","2"]`},
			NodeJSON: `{
					  "NodeID": "8b7cd8b7-75f3-cc91-2598-9fbf6ba9db68",
					  "NodeName": "工作流节点--最外层",
					  "NodeType": "WORKFLOW_REF",
					  "NodeDesc": "回复",
					  "WorkflowRefNodeData": {
						"WorkflowID": "a1f9eb4c-13e0-48f4-9d02-474185e90c9d",
						"RefInputs": [
						  {
							"Name": "input_str",
							"Type": "STRING",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
							  "InputType": "CUSTOM_VARIABLE",
							  "CustomVarID": "e3682949-a887-4fab-aed8-7139bf165c97"
							}
						  },
						  {
							"Name": "input_int",
							"Type": "INT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
							  "InputType": "CUSTOM_VARIABLE",
							  "CustomVarID": "bfc0cd02-71ba-437a-a175-9e51e3849e33"
							}
						  },
						  {
							"Name": "input_obj",
							"Type": "OBJECT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
							  "InputType": "CUSTOM_VARIABLE",
							  "CustomVarID": "0d277e0e-5d8c-4c8a-93ee-d4988b6d0798"
							}
						  },
						  {
							"Name": "input_arr_obj",
							"Type": "ARRAY_OBJECT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
							  "InputType": "CUSTOM_VARIABLE",
							  "CustomVarID": "ec150ea1-96c5-4214-8318-d3c77069a477"
							}
						  }
						]
					  }
					}`,
		},
	}
	loopNode290BaseReqs = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs:      map[string]string{"Input": `["1","2","3"]`, "arr_obj": `[{"a": "1"}, {"a": "2"}]`},
			NodeJSON: `{
				  "NodeID": "f5049ac9-c045-cfad-db21-32414782676b",
				  "NodeName": "循环1",
				  "NodeType": "ITERATION",
				  "NodeDesc": "",
				  "Inputs": [
					{
					  "Name": "Input",
					  "Type": "ARRAY_STRING",
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "5fffece3-f10b-4517-aed7-75383ef35b49"
					  },
					  "Desc": "循环数组变量",
					  "nameTip": "该变量仅支持array<string>和array<object>类型",
					  "controlShowType": [
						"ARRAY_OBJECT",
						"ARRAY_STRING"
					  ],
					  "SubInputs": []
					},
					{
					  "Name": "arr_obj",
					  "Type": "ARRAY_OBJECT",
					  "Desc": "",
					  "IsRequired": false,
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "ec150ea1-96c5-4214-8318-d3c77069a477"
					  },
					  "SubInputs": []
					}
				  ],
				  "IterationNodeData": {
					"BodyType": "WORKFLOW",
						"WorkflowID": "a64b5b68-251d-40b0-92e4-e00924f0ffe9",
						"SpecifiedTraversalVariable": "arr_obj",
						"RefInputs": [
						  {
							"Name": "input_str",
							"Type": "STRING",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Input.Item"
							}
						  },
						  {
							"Name": "input_int",
							"Type": "INT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Index"
							}
						  },
						  {
							"Name": "input_obj",
							"Type": "OBJECT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Output"
							}
						  },
						  {
							"Name": "input_arr_obj",
							"Type": "ARRAY_OBJECT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "arr_obj"
						}
					  }
					],
					"IterationMode": "ALL",
					"Condition": {
					  "LogicalOperator": "UNSPECIFIED",
					  "Comparison": {
						"Left": {
							  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"LeftType": "STRING",
						"Operator": "UNSPECIFIED",
						"Right": {
						  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"MatchType": "SEMANTIC"
							}
						  }
					  }
					}`,
		},
	}
	loopNode290MultiLevelReqs = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs:      map[string]string{"Input": `["1","2"]`},
			CustomVariables: map[string]*KEP_WF_DM.Variable{
				"array_string": {
					Name:  "array_string",
					Value: `["3","4"]`,
				},
			},
			NodeJSON: `{
				  "NodeID": "f5049ac9-c045-cfad-db21-32414782676b",
				  "NodeName": "循环1",
				  "NodeType": "ITERATION",
				  "NodeDesc": "",
				  "Inputs": [
					{
					  "Name": "Input",
					  "Type": "ARRAY_STRING",
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "5fffece3-f10b-4517-aed7-75383ef35b49"
					  },
					  "Desc": "循环数组变量",
					  "nameTip": "该变量仅支持array<string>和array<object>类型",
					  "controlShowType": [
						"ARRAY_OBJECT",
						"ARRAY_STRING"
					  ],
					  "SubInputs": []
					},
					{
					  "Name": "arr_obj",
					  "Type": "ARRAY_OBJECT",
					  "Desc": "",
					  "IsRequired": false,
					  "Input": {
						"InputType": "CUSTOM_VARIABLE",
						"CustomVarID": "ec150ea1-96c5-4214-8318-d3c77069a477"
					  },
					  "SubInputs": []
					}
				  ],
				  "IterationNodeData": {
					"BodyType": "WORKFLOW",
						"WorkflowID": "loop_workflow_001",
						"RefInputs": [
						  {
							"Name": "input_arr_str",
							"Type": "ARRAY_STRING",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
							  "InputType": "NODE_INPUT_PARAM",
							  "NodeInputParamName": "Input"
							}
						  },
						  {
							"Name": "input_int",
							"Type": "INT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Index"
							}
						  },
						  {
							"Name": "input_obj",
							"Type": "OBJECT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "Loop.Output"
							}
						  },
						  {
							"Name": "input_arr_obj",
							"Type": "ARRAY_OBJECT",
							"Desc": "",
							"IsRequired": false,
							"SubInputs": [],
							"Input": {
						  "InputType": "NODE_INPUT_PARAM",
						  "NodeInputParamName": "arr_obj"
						}
					  }
					],
					"IterationMode": "ALL",
					"Condition": {
					  "LogicalOperator": "UNSPECIFIED",
					  "Comparison": {
						"Left": {
							  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"LeftType": "STRING",
						"Operator": "UNSPECIFIED",
						"Right": {
						  "InputType": "CUSTOM_VARIABLE",
						  "CustomVarID": ""
						},
						"MatchType": "SEMANTIC"
							}
						  }
					  }
					}`,
		},
	}
	workflowNode290MultiLevelReqs = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs:      map[string]string{"Input": `["1","2"]`},
			CustomVariables: map[string]*KEP_WF_DM.Variable{
				"array_string": {
					Name:  "array_string",
					Value: `["3","4"]`,
				},
			},
			NodeJSON: `{
      "NodeID": "38d8803b-ab37-c4c0-0b65-06c3ab40e1dc",
      "NodeName": "B1",
      "NodeDesc": "B",
      "NodeType": "WORKFLOW_REF",
      "WorkflowRefNodeData": {
        "WorkflowID": "f4fc0272-94ef-412a-b7a0-9708aeea1c4e",
        "RefInputs": []
      },
      "Inputs": [],
      "Outputs": [
        {
          "Title": "Output",
          "Type": "OBJECT",
          "Required": [],
          "Properties": [
            {
              "Title": "B_output_value_is_Cworkflow_Output",
              "Type": "OBJECT",
              "Required": [],
              "Properties": [],
              "Desc": "",
              "Value": {
                "InputType": "REFERENCE_OUTPUT",
                "Reference": {
                  "NodeID": "91802d8a-49a6-f51f-304f-4d02b661c961",
                  "JsonPath": "Output"
                }
              },
              "AnalysisMethod": "COVER"
            }
          ],
          "Desc": "输出内容",
          "AnalysisMethod": "COVER"
        }
      ],
      "NextNodeIDs": [
        "05f54723-1b89-cfb7-1de3-53363376a9f9"
      ]
    }`,
		},
	}
	loopNode290Reqs = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs:      map[string]string{"ai": `[1,2,3,4]`, "ao": `[{"a":1,"b":2},{"a":2,"b":3},{"a":3,"b":4},{"a":4,"b":5},{"a":5,"b":6}]`},
			CustomVariables: map[string]*KEP_WF_DM.Variable{
				"array_string": {
					Name:  "array_string",
					Value: `["3","4"]`,
				},
			},
			NodeJSON: `{
      "NodeID": "724d9c19-634b-36fb-3065-7d5123b46838",
      "NodeName": "批处理1",
      "NodeDesc": "",
      "NodeType": "BATCH",
      "BatchNodeData": {
        "BodyType": "WORKFLOW",
        "WorkflowID": "2808a1af-3db1-47fa-9fd4-0e85c46b342f",
        "RefInputs": [
          {
            "Name": "arrobj",
            "Type": "ARRAY_OBJECT",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "arrobj"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          },
          {
            "Name": "str",
            "Type": "STRING",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "str"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          },
          {
            "Name": "doc",
            "Type": "STRING",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "doc"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          },
          {
            "Name": "item",
            "Type": "OBJECT",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "arrobj.Item"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          }
        ],
        "MaxParallel": 3,
        "SpecifiedTraversalVariable": "arrobj"
      },
      "Inputs": [
        {
          "Name": "arrobj",
          "Type": "ARRAY_OBJECT",
          "Input": {
            "InputType": "CUSTOM_VARIABLE",
            "CustomVarID": "ec1cf0b5-4ca4-4d45-98a0-8d7b47d254c0"
          },
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "DefaultValue": "",
          "DefaultFileName": ""
        },
        {
          "Name": "str",
          "Type": "STRING",
          "Input": {
            "InputType": "CUSTOM_VARIABLE",
            "CustomVarID": "699dda0b-7f44-486b-9a73-aee6062d3d42"
          },
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "DefaultValue": "",
          "DefaultFileName": ""
        },
        {
          "Name": "doc",
          "Type": "DOCUMENT",
          "Input": {
            "InputType": "CUSTOM_VARIABLE",
            "CustomVarID": "c741c3df-ef4c-409f-9d52-cd410fe9560f"
          },
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "DefaultValue": "",
          "DefaultFileName": ""
        }
      ],
      "Outputs": [],
      "NextNodeIDs": [
        "6df42042-ab84-b406-ddc0-9356d0a9f1b8"
      ],
      "NodeUI": "{\"data\":{\"content\":{\"maxParallel\":3,\"workflowRefNodeName\":\"层级2\"},\"isHovering\":false,\"isParallel\":false,\"source\":true,\"target\":true,\"debug\":null,\"error\":false,\"output\":[{\"label\":\"Output\",\"desc\":\"输出内容\",\"optionType\":\"REFERENCE_OUTPUT\",\"type\":\"OBJECT\",\"children\":[]}]},\"position\":{\"x\":1034,\"y\":292},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":true,\"measured\":{\"width\":250,\"height\":104}}"
    }`,
		},
	}
	multiLevelMultiType290 = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs:      map[string]string{"arrobj": `[{"a":1,"b":2}]`},
			CustomVariables: map[string]*KEP_WF_DM.Variable{
				"array_string": {
					Name:  "array_string",
					Value: `["3","4"]`,
				},
			},
			NodeJSON: `{
      "NodeID": "724d9c19-634b-36fb-3065-7d5123b46838",
      "NodeName": "批处理1",
      "NodeDesc": "",
      "NodeType": "BATCH",
      "BatchNodeData": {
        "BodyType": "WORKFLOW",
        "WorkflowID": "2808a1af-3db1-47fa-9fd4-0e85c46b342f",
        "RefInputs": [
          {
            "Name": "arrobj",
            "Type": "ARRAY_OBJECT",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "arrobj"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          },
          {
            "Name": "str",
            "Type": "STRING",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "str"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          },
          {
            "Name": "doc",
            "Type": "STRING",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "doc"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          },
          {
            "Name": "item",
            "Type": "OBJECT",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "arrobj.Item"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          }
        ],
        "MaxParallel": 3,
        "SpecifiedTraversalVariable": "arrobj"
      },
      "Inputs": [
        {
          "Name": "arrobj",
          "Type": "ARRAY_OBJECT",
          "Input": {
            "InputType": "CUSTOM_VARIABLE",
            "CustomVarID": "ec1cf0b5-4ca4-4d45-98a0-8d7b47d254c0"
          },
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "DefaultValue": "",
          "DefaultFileName": ""
        },
        {
          "Name": "str",
          "Type": "STRING",
          "Input": {
            "InputType": "CUSTOM_VARIABLE",
            "CustomVarID": "699dda0b-7f44-486b-9a73-aee6062d3d42"
          },
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "DefaultValue": "",
          "DefaultFileName": ""
        },
        {
          "Name": "doc",
          "Type": "DOCUMENT",
          "Input": {
            "InputType": "CUSTOM_VARIABLE",
            "CustomVarID": "c741c3df-ef4c-409f-9d52-cd410fe9560f"
          },
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "DefaultValue": "",
          "DefaultFileName": ""
        }
      ],
      "Outputs": [],
      "NextNodeIDs": [
        "6df42042-ab84-b406-ddc0-9356d0a9f1b8"
      ]
    }`,
		},
	}
	batchNodeBaseReqs = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs:      map[string]string{"Input": `["1","2","3"]`, "arr_obj": `[{"a": "1"}, {"a": "2"}, {"a": "3"}, {"a": "4"}, {"a": "5"}]`},
			NodeJSON: `{
			"NodeID": "f5049ac9-c045-cfad-db21-32414782676b",
      "NodeName": "批处理1",
			"NodeType": "BATCH",
      "NodeDesc": "",
			"Inputs": [
				{
				"Name": "Input",
				"Type": "ARRAY_STRING",
				"Input": {
					"InputType": "CUSTOM_VARIABLE",
					"CustomVarID": "5fffece3-f10b-4517-aed7-75383ef35b49"
				},
				"Desc": "循环数组变量",
				"nameTip": "该变量仅支持array<string>和array<object>类型",
				"controlShowType": [
					"ARRAY_OBJECT",
					"ARRAY_STRING"
				],
				"SubInputs": []
				},
				{
				"Name": "arr_obj",
				"Type": "ARRAY_OBJECT",
				"Desc": "",
				"IsRequired": false,
				"Input": {
					"InputType": "CUSTOM_VARIABLE",
					"CustomVarID": "ec150ea1-96c5-4214-8318-d3c77069a477"
				},
				"SubInputs": []
				}
			],
      "BatchNodeData": {
        "BodyType": "WORKFLOW",
				"WorkflowID": "a64b5b68-251d-40b0-92e4-e00924f0ffe9",
				"SpecifiedTraversalVariable": "arr_obj",
				"MaxParallel": 3,
        "RefInputs": [
          {
						"Name": "input_str",
            "Type": "STRING",
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
							"NodeInputParamName": "Input.Item"
						}
            },
					{
						"Name": "input_int",
						"Type": "INT",
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
          "Input": {
							"InputType": "NODE_INPUT_PARAM",
							"NodeInputParamName": "Loop.Index"
            }
          },
					{
						"Name": "input_obj",
						"Type": "OBJECT",
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "Input": {
							"InputType": "NODE_INPUT_PARAM",
							"NodeInputParamName": "Loop.Output"
            }
          },
        {
						"Name": "input_arr_obj",
          "Type": "ARRAY_OBJECT",
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
						"Input": {
							"InputType": "NODE_INPUT_PARAM",
							"NodeInputParamName": "arr_obj"
						}
        }
				]
				}
    }`,
		},
	}
	batchNodeBaseReqsBug = []*KEP_WF_DM.DebugWorkflowNodeDialogRequest{
		{
			SessionID:   "test-session",
			AppID:       AppID,
			RequestType: KEP_WF_DM.RequestType_RUN,
			Inputs: map[string]string{
				"arri": `[
					1,
					2
				]`,
				"arrobj": `[
					{
						"x": "x1",
						"y": "y1"
					},
					{
						"x": "x2",
						"y": "y2"
					}
				]`,
				"arrs": `[
					"a1",
					"a2",
					"a3",
					"a4"
				]`,
			},
			NodeJSON: `{
      "NodeID": "9b4eef0a-beb4-64bc-8c2f-6ff7a9afd031",
      "NodeName": "批处理1",
      "NodeDesc": "",
      "NodeType": "BATCH",
      "BatchNodeData": {
        "BodyType": "WORKFLOW",
        "WorkflowID": "7e4eea0e-1ba1-4255-9ba1-ccc8dd8167f6",
        "RefInputs": [
          {
            "Name": "int",
            "Type": "INT",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "arri.Item"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          },
          {
            "Name": "str",
            "Type": "STRING",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "arrs.Item"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          },
          {
            "Name": "obj",
            "Type": "OBJECT",
            "Input": {
              "InputType": "NODE_INPUT_PARAM",
              "NodeInputParamName": "arrobj.Item"
            },
            "Desc": "",
            "IsRequired": false,
            "SubInputs": [],
            "DefaultValue": "",
            "DefaultFileName": ""
          }
        ],
        "MaxParallel": 2,
        "SpecifiedTraversalVariable": "arrs"
      },
      "Inputs": [
        {
          "Name": "arri",
          "Type": "ARRAY_INT",
          "Input": {
            "InputType": "REFERENCE_OUTPUT",
            "Reference": {
              "NodeID": "623873d4-7454-2dc0-5121-dca21fb67501",
              "JsonPath": "Output.arr_i"
            }
          },
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "DefaultValue": "",
          "DefaultFileName": ""
        },
        {
          "Name": "arrs",
          "Type": "ARRAY_STRING",
          "Input": {
            "InputType": "REFERENCE_OUTPUT",
            "Reference": {
              "NodeID": "623873d4-7454-2dc0-5121-dca21fb67501",
              "JsonPath": "Output.arr_s"
            }
          },
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "DefaultValue": "",
          "DefaultFileName": ""
        },
        {
          "Name": "arrobj",
          "Type": "ARRAY_OBJECT",
          "Input": {
            "InputType": "REFERENCE_OUTPUT",
            "Reference": {
              "NodeID": "623873d4-7454-2dc0-5121-dca21fb67501",
              "JsonPath": "Output.arr_obj"
            }
          },
          "Desc": "",
          "IsRequired": false,
          "SubInputs": [],
          "DefaultValue": "",
          "DefaultFileName": ""
        }
      ],
      "Outputs": [],
      "NextNodeIDs": [
        "77ee5b8f-068c-0a9f-acfa-d8e385566fae"
      ],
      "NodeUI": "{\"data\":{\"content\":{\"maxParallel\":2,\"workflowRefNodeName\":\"arr类型\"},\"isHovering\":false,\"isParallel\":false,\"source\":true,\"target\":true,\"debug\":null,\"error\":false,\"output\":[{\"label\":\"Output\",\"desc\":\"输出内容\",\"optionType\":\"REFERENCE_OUTPUT\",\"type\":\"OBJECT\",\"children\":[]}]},\"position\":{\"x\":1008,\"y\":282},\"targetPosition\":\"left\",\"sourcePosition\":\"right\",\"selected\":true,\"measured\":{\"width\":250,\"height\":104},\"dragging\":false}"
    }`,
		},
	}
)
