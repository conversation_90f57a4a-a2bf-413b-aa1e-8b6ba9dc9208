package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	llmm "git.woa.com/dialogue-platform/proto/pb-stub/llm-manager-server"
	"git.woa.com/ivy/qbot/qbot/chat/helper"
	"git.woa.com/ivy/qbot/qbot/chat/internal/config"
	"git.woa.com/ivy/qbot/qbot/chat/internal/eventbus/botsession"
	"google.golang.org/protobuf/proto"
)

type chatFunc func(ctx context.Context, req *llmm.Request,
	ch chan *llmm.Response, startTime time.Time, signal chan int) error

func (a *ChatAgentImpl) qqBrowserReq(bs *botsession.BotSession, req *llmm.Request) {
	// for  浏览器 特殊逻辑，系统Prompt特殊传递，浏览器的模版特殊处理
	currentAgent := bs.AgentStatus.CurrentAgent
	sysPrompt, err := a.agentContainer.GetAgentInstructions(currentAgent)
	if err != nil || len(sysPrompt) == 0 {
		sysPrompt = config.GetAgentConfig().QBConfig.SystemPrompt
	}
	newMessage := []*llmm.Message{{
		Role:    llmm.Role_SYSTEM,
		Content: sysPrompt,
	}}
	// 处理浏览器的插件特殊处理逻辑，浏览器工具只有最后一轮保留输出，其他轮浏览器工具都清空输出
	qqBrowerToolsMap := map[string]struct{}{}
	for _, tool := range config.GetAgentConfig().QBConfig.BrowserTools {
		qqBrowerToolsMap[tool] = struct{}{}
	}
	toolCallIDMap := map[string]string{} // callid -> tool name 的映射
	// 先预处理出来 toolcallid 的 map
	for _, message := range req.GetMessages() {
		if message.GetRole() == llmm.Role_ASSISTANT {
			for _, toolcall := range message.GetToolCalls() {
				toolCallIDMap[toolcall.GetId()] = toolcall.GetFunction().GetName()
			}
		}
	}
	// 从后往前扫描
	lastToolOut := false
	for i := len(req.GetMessages()) - 1; i >= 0; i-- {
		clearToolsOuput := false
		message := req.GetMessages()[i]
		if message.GetRole() == llmm.Role_TOOL {
			// 浏览器的工具需要特殊处理
			if !lastToolOut {
				lastToolOut = true
			} else {
				// 非最后一个工具输出
				if toolname, ok := toolCallIDMap[message.GetToolCallId()]; ok {
					if _, isQQBorwerTool := qqBrowerToolsMap[toolname]; isQQBorwerTool {
						// 确定是浏览器的工具，清空输出
						clearToolsOuput = true
					}
				}
			}
		}
		if clearToolsOuput {
			req.GetMessages()[i].Content = strings.Split(req.GetMessages()[i].Content, "Page Content")[0]
		}
	}
	newMessage = append(newMessage, req.GetMessages()...)
	req.Messages = newMessage
	req.ExtraParams = nil
}

// chatDecorator chat 接口装饰器，functional call 的时候，用户自定义参数不传给模型
func (a *ChatAgentImpl) chatDecorator(f chatFunc, bs *botsession.BotSession,
	placeholders map[string]string) chatFunc {
	return func(ctx context.Context, req *llmm.Request, ch chan *llmm.Response,
		startTime time.Time, signal chan int) error {
		if req == nil {
			return fmt.Errorf("req is nil")
		}

		newReq := proto.Clone(req).(*llmm.Request)
		if config.IsQBAppBizID(bs.App.GetAppBizId()) {
			a.qqBrowserReq(bs, newReq)
			log.InfoContextf(ctx, "qqBrowserReq: %v", helper.Object2String(newReq))
		}
		a.processBrowserTools(ctx, newReq)
		// 调用原始函数，并自动完成长链和短链的转换
		err := chatReqShortLinkDecorator(f, placeholders)(ctx, newReq, ch, startTime, signal)
		return err
	}
}

// chatRspVarDecorator chat 回包装饰器，配置用户参数到工具参数
func (a *ChatAgentImpl) chatRspVarDecorator(ctx context.Context,
	rsp *llmm.Response, bs *botsession.BotSession) error {
	if rsp == nil {
		return nil
	}
	if !rsp.Finished {
		// rsp 没有结束不替换
		return nil
	}
	for _, rspTool := range rsp.GetMessage().GetToolCalls() {
		callParams := map[string]interface{}{} // 调用工具的参数
		if err := json.Unmarshal([]byte(rspTool.Function.Arguments), &callParams); err != nil {
			// 如果 Arguments 无法 json 序列化，不做任何处理
			continue
		}
		newCallParams := map[string]interface{}{}
		if _, isLocal := a.agentContainer.AgentNameLocalToolMap[rspTool.Function.Name]; isLocal {
			// 本地工具直接用 CustomVariables 全量替换
			// 遍历模型生成出来的参数
			for callParam, callValue := range callParams {
				if uservalue, ok := bs.CustomVariables[callParam]; ok {
					// 如果 call 参数在用户自定义参数列表中，用用户自定义参数替换
					newCallParams[callParam] = uservalue
				} else {
					newCallParams[callParam] = callValue // 还是用模型提取的值
				}
			}
			rspTool.Function.Arguments = helper.Object2String(newCallParams)
		} else if toolInfo, ok := bs.ToolsInfo[rspTool.Function.Name]; ok && !toolInfo.IsWorkflow {
			// 如果是云上工具且非工作流工具，按照变量的定义规则替换默认值
			if err := a.generalReplaceDefaultValue(ctx, bs, toolInfo.AgentToolInfo.GetInputs(),
				callParams); err != nil {
				log.ErrorContextf(ctx, "generalReplaceDefaultValue failed on tool %s(%s): %v",
					toolInfo.GetToolName(), toolInfo.GetToolId(), err)
				return err
			}
			rspTool.Function.Arguments = helper.Object2String(callParams)
		}
	}
	return nil
}
