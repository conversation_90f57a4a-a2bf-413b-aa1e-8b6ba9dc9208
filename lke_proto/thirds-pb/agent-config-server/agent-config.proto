syntax = "proto3";

package trpc.KEP.agent_config_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/agent_config_server";

import "trpc.proto";
import "validate.proto";

import "plugin-config-server/plugin-config.proto";
import "bot-task-config-server/data-sync.proto";

// 应用Agent配置 - 前端接口
service AgentConfig {
  // Echo 方法用于测试服务是否部署成功
  // @alias=/Echo
  rpc Echo (EchoReq) returns (EchoRsp) {}

  // 查询Agent配置信息
  // @alias=/DescribeAgentConfig
  rpc DescribeAgentConfig(DescribeAgentConfigReq) returns (DescribeAgentConfigRsp){}

  // 创建Agent
  // @alias=/CreateAgent
  rpc CreateAgent(CreateAgentReq) returns (CreateAgentRsp){}

  // 创建启动Agent
  // @alias=/CreateStartingAgent
  rpc CreateStartingAgent(CreateStartingAgentReq) returns (CreateStartingAgentRsp){}

  // 复制Agent，一般用来导入PDL作为协同Agent
  // @alias=/CopyAgent
  rpc CopyAgent(CopyAgentReq) returns (CopyAgentRsp){}

  // 复制Agent模式的App，一般用来复制Agent模式的App下的所有Agent
  // @alias=/CopyAgentApp
  rpc CopyAgentApp(CopyAgentAppReq) returns (CopyAgentAppRsp){}

  // 删除Agent
  // @alias=/DeleteAgent
  rpc DeleteAgent(DeleteAgentReq) returns (DeleteAgentRsp){}

  // 修改Agent
  // @alias=/ModifyAgent
  rpc ModifyAgent(ModifyAgentReq) returns (ModifyAgentRsp){}

  // 修改Agent转交关系列表
  // @alias=/ModifyAgentHandoffList
  rpc ModifyAgentHandoffList(ModifyAgentHandoffListReq) returns (ModifyAgentHandoffListRsp){}

  // 查询指定应用下的Agent列表
  // @alias=/DescribeAppAgentList
  rpc DescribeAppAgentList(DescribeAppAgentListReq) returns (DescribeAppAgentListRsp) {}

  // 查询Agent列表
  // @alias=/DescribeAgentList
  rpc DescribeAgentList(DescribeAgentListReq) returns (DescribeAgentListRsp) {}

  // ======================= 发布 ==========================
  // 获取应用Agent发布列表
  // @alias=/ListAppAgentReleasePreview
  rpc ListAppAgentReleasePreview(ListAppAgentReleasePreviewReq) returns (ListAppAgentReleasePreviewRsp);
  // ======================================================
}

// 内部接口
service AgentConfigApi {
  // ======================= Agent插件 ==========================
  // 创建启动Agent，可重入
  // @alias=/CreateStartingAgent
  rpc CreateStartingAgent(CreateStartingAgentReq) returns (CreateStartingAgentRsp){}

  // 复制Agent模式的App，一般用来复制Agent模式的App下的所有Agent
  // @alias=/CopyAgentApp
  rpc CopyAgentApp(CopyAgentAppReq) returns (CopyAgentAppRsp){}

  // 查询指定应用下的Agent列表
  // @alias=/DescribeAppAgentList
  rpc DescribeAppAgentList(DescribeAppAgentListReq) returns (DescribeAppAgentListRsp) {}

  // 查询引用指定插件工具的Agent列表
  // @alias=/DescribeAgentHasReferencedPluginToolList
  rpc DescribeAgentHasReferencedPluginToolList(DescribeAgentHasReferencedPluginToolListReq) returns (DescribeAgentHasReferencedPluginToolListRsp) {}

  // ===========================================================


  // ======================= 发布任务 ==========================

  // 获取未发布的数量
  // @alias=/GetUnreleasedCount
  rpc GetUnreleasedCount(trpc.KEP.bot_task_config_server.GetUnreleasedCountReq) returns (trpc.KEP.bot_task_config_server.GetUnreleasedCountRsp);

  // 发送同步数据任务事件, 任务采集、任务发布、任务暂停重试
  // @alias=/SendDataSyncTaskEvent
  rpc SendDataSyncTaskEvent(trpc.KEP.bot_task_config_server.SendDataSyncTaskEventReq) returns (trpc.KEP.bot_task_config_server.SendDataSyncTaskEventRsp);

  // [单个]获取同步任务, 详情, 状态等
  // @alias=/GetDataSyncTask
  rpc GetDataSyncTask(trpc.KEP.bot_task_config_server.GetDataSyncTaskReq) returns (trpc.KEP.bot_task_config_server.GetDataSyncTaskRsp);

  // ======================= 清理任务 ==========================
  // 清理任务/指定应用下的Agent相关资源
  // @alias=/ClearAgentAppResource
  rpc ClearAgentAppResource(ClearAgentAppResourceReq) returns (ClearAgentAppResourceRsp);
  // ===========================================================


  // ======================= 数据迁移 ==========================

  // Agent数据库迁移接口，内部专用
  // @alias=/AgentMigrate
  rpc AgentMigrate(AgentMigrateReq) returns (AgentMigrateRsp);

  // ===========================================================

}

message EchoReq {}

message EchoRsp {}

// Agent模型信息
// borrowed from https://git.woa.com/dialogue-platform/lke_proto/blob/master/thirds-pb/bot-admin-config-server/bot-admin-api.proto#L1292
// 目前只用到如下字段：history_limit, model_name, temperature, top_p, 并新增字段model_context_words_limit, instructions_words_limit, model_alias_name
// TODO：建议直接引用，不过需要将AgentModelInfo单独放到类似"bot-admin-config-server/bot-admin-config.proto"，这个proto不能引用其他proto
// import "bot-admin-config-server/bot-admin-api.proto";
message AgentModelInfo {
  // 提示词
  string prompt = 1[json_name = "Prompt"];
  // 提示词内容字符限制
  uint32 prompt_words_limit = 2[json_name = "PromptWordsLimit"];
  // 对话历史条数限制
  uint32 history_limit = 6[json_name = "HistoryLimit"];
  // 对话历史条数限制
  uint32 history_words_limit = 7[json_name = "HistoryWordsLimit"];
  // 模型名称 (trpc 接口使用)
  string model_name = 8[json_name = "ModelName"];
  // 下游服务名 (trpc 接口使用)
  string service_name = 9[json_name = "ServiceName"];
  // 模型是否开启
  bool is_enabled = 10[json_name = "IsEnabled"];

  // 模型调用接口路由 (http 接口使用)
  string path = 3 [json_name = "Path", deprecated = true];
  // 模型调用接口地址 (http 接口使用)
  string target = 4 [json_name = "Target", deprecated = true];
  // 模型类型 (http 接口使用)
  uint32 type = 5 [json_name = "Type", deprecated = true];

  // 对话限制长度
  uint32 chat_words_limit = 11[json_name = "ChatWordsLimit"];
  // 模型的top_k配置
  uint32 top_k = 12[json_name = "TopK"];
  // 指定版本的prompt
  map<string, string> prompts = 13[json_name = "Prompts"];
  // 温度
  float temperature = 14[json_name = "Temperature"];
  // TopP
  float top_p = 15[json_name = "TopP"];
  // prompt版本
  string prompt_version = 16[json_name = "PromptVersion"];
  // 模型上下文长度字符限制
  string model_context_words_limit = 17[json_name = "ModelContextWordsLimit"];
  // 指令长度字符限制
  uint32 instructions_words_limit = 18[json_name = "InstructionsWordsLimit"];
  // 模型别名
  string model_alias_name = 19[json_name = "ModelAliasName"];
}

// Agent信息
message Agent {
  string AgentId = 1; // AgentID，全局唯一索引
  string WorkflowId = 2; // WorkflowID，非空则当前Agent从workflow转换而来

  string Name = 3; // Agent名称，同一个应用内，Agent名称不能重复
  string IconUrl = 4; // 插件图标url

  // 注意，当AgentType为AGENT_INFO_TYPE_KB_AGENT时
  string Instructions = 5; // Agent指令；当该Agent被调用时，将作为“系统提示词”使用，描述Agent应执行的操作和响应方式
  string HandoffDescription = 6; // 当Agent作为转交目标时的描述，用于让其他Agent的LLM理解其功能和转交时机
  repeated string Handoffs = 7;  // Agent可转交的子AgentId列表
  AgentModelInfo Model = 8; // Agent调用LLM时使用的模型配置

  repeated AgentToolInfo Tools = 9; // Agent可使用的工具列表
  repeated AgentPluginInfo Plugins = 10; // Agent可使用的工具列表

  bool IsStartingAgent = 11; // 当前Agent是否是启动Agent

  AgentTypeEnum AgentType = 12; // Agent类型
}

// 知识库检索Agent提示词
message AgentInstructionsForKbAgent{
  string RetrieveRule = 1;    // 检索规则
  string ExampleQa = 2;       // 示例问答

  // 以下字段为查询接口专用
  repeated AgentKnowledgeSchema KnowledgeSchemas = 3;// 知识库Schema -- 查询接口使用, 需要实时去应用中心拉取
}

message AgentKnowledgeSchema {
  string BusinessId = 1; // 文档："doc_xxxxx" 文档聚类："doc_cluster_xxxxx"
  string Name = 2; // 名称，文件名或者文件聚类名
  string Summary = 3; // 文件摘要或者文件聚类摘要
}

enum EnvType {
  TEST = 0; // 测试环境
  PROD = 1; // 正式环境
}

message DescribeAgentConfigReq {
  string AppBizId = 1;   // 应用ID
  string ModelName = 2; // 模型名称, 为空则获取应用下所有模型配置
}

message DescribeAgentConfigRsp {
  repeated AgentModelInfo ModelInfos = 1; // 应用下的模型配置列表

  message AgentModelInfo {
    // 模型名称 (trpc 接口使用)
    string model_name = 8[json_name = "ModelName"];
    // 指令长度字符限制, 为0则不限制
    uint32 instructions_words_limit = 18[json_name = "InstructionsWordsLimit"];
  }
}

message CreateAgentReq {
  string AppBizId = 1;   // 应用ID
  Agent Agent = 2; // Agent信息, 应用创建的第一个Agent为启动Agent
}

message CreateAgentRsp {
  string AgentId = 1;  // AgentID，全局唯一索引
}

message CreateStartingAgentReq {
  string AppBizId = 1;   // 应用ID
  Agent Agent = 2; // Agent信息，如果应用不存在启动Agent，则创建此启动Agent
}

message CreateStartingAgentRsp {
  string AgentId = 1;  // AgentID，全局唯一索引
}

message CopyAgentReq {
  string SourceAppBizId = 1;     // 源应用ID
  string SourceAgentId = 2;      // 源AgentID，全局唯一索引，非空则直接复制这个Agent
  string SourceWorkflowId = 3;   // 源WorkflowID，AgentId为空则转换这个workflow为Agent并复制Agent
  string SourceWorkflowName = 4; // 源Workflow名称，workflow转换为Agent时作为新Agent的名称

  string TargetAppBizId = 5;  // 目的应用ID
}

message CopyAgentRsp {
  string TargetAgentId = 1; // 复制后新的目的AgentID
}

message CopyAgentAppReq {
  string SourceAppBizId = 1;     // 源应用ID
  bool Force = 3; // 是否强制复制，如果为true，则会删除目标应用的所有Agent，并重新创建

  string TargetAppBizId = 2;  // 目的应用ID
}

message CopyAgentAppRsp {
}

message DeleteAgentReq {
  string AppBizId = 1; // 应用ID， 必填
  string AgentId = 2;  // AgentID， 必填
}

message DeleteAgentRsp {
  string AgentId = 1;  // AgentID
}

message ModifyAgentReq {
  string AppBizId = 1;   // 应用ID， 必填
  Agent Agent = 2; // 更新后的Agent信息
}

message ModifyAgentRsp {
}

message ModifyAgentHandoffListReq {
  string AppBizId = 1; // 应用ID
  repeated Agent Agents = 2;    // Agent可转交的子Agent列表

  message Agent{
    string AgentId = 1; // AgentID
    repeated string Handoffs = 2; // Agent可转交的子AgentId列表
  }
}

message ModifyAgentHandoffListRsp {
}

message DescribeAgentListReq {
  string AppBizId = 1; // appid
  string SearchWord = 2; // 过滤词

  // 分页参数
  int32 PageSize = 3;   // 每页数目
  int32 PageNumber = 4; // 页码
}

message DescribeAgentListRsp {
  repeated AppAgent  Agents = 1; // 应用Agent信息列表

  // 分页参数
  uint32 Total = 2; // 总数目

  message AppAgent{
    Agent Agent = 1; // Agent信息列表
    string AppBizId = 2; // 应用ID
    string AppName = 3; // 应用名称
    string From = 4; // 来源
    bool IsWorkflow = 5; // 是否是workflow
    string Developer = 6; // 开发者
  }

  message Agent {
    string AgentId = 1; // AgentID，全局唯一索引
    string WorkflowId = 2; // WorkflowID，非空则当前Agent从workflow转换而来

    string Name = 3; // Agent名称，同一个应用内，Agent名称不能重复
    string IconUrl = 4; // 插件图标url

    string Instructions = 5; // Agent指令；当该Agent被调用时，将作为“系统提示词”使用，描述Agent应执行的操作和响应方式
    string HandoffDescription = 6; // 当Agent作为转交目标时的描述，用于让其他Agent的LLM理解其功能和转交时机
    repeated string Handoffs = 7;    // Agent可转交的子AgentId列表
    AgentModelInfo Model = 8; // Agent调用LLM时使用的模型配置

    repeated AgentToolInfo Tools = 9; // Agent可使用的工具列表
    repeated AgentPluginInfo Plugins = 10; // Agent可使用的工具列表

    bool IsStartingAgent = 11; // 当前Agent是否是启动Agent

    AgentTypeEnum AgentType = 12;   // Agent类型
  }

  // Agent的插件信息（前端用）
  message AgentPluginInfo {
    string PluginId = 1; // 插件ID
    repeated AgentPluginHeader Headers = 2; // 应用配置的插件header信息
    AgentModelInfo Model = 3; // 插件调用LLM时使用的模型配置，一般用于指定知识库问答插件的生成模型

    // 以下为定制插件专用
    PluginInfoTypeEnum PluginInfoType = 10;           // 插件类型
    AgentKnowledgeQAPlugin  KnowledgeQa = 11; // 知识库问答插件配置 when PluginType = PLUGIN_TYPE_KNOWLEDGE_QA

    enum PluginInfoTypeEnum {
      PLUGIN_INFO_TYPE_UNSPECIFIED = 0;  // 保留字段，兼容旧版本
      PLUGIN_INFO_TYPE_KNOWLEDGE_QA = 1; // 知识库问答插件
    }
  }

  message AgentModelInfo {
    // 对话历史条数限制
    uint32 history_limit = 6[json_name = "HistoryLimit"];
    // 模型名称 (trpc 接口使用)
    string model_name = 8[json_name = "ModelName"];
    // 模型是否开启
    bool is_enabled = 10[json_name = "IsEnabled"];
    // 温度
    float temperature = 14[json_name = "Temperature"];
    // TopP
    float top_p = 15[json_name = "TopP"];
    // 模型上下文长度字符限制
    string model_context_words_limit = 17[json_name = "ModelContextWordsLimit"];
    // 指令长度字符限制
    uint32 instructions_words_limit = 18[json_name = "InstructionsWordsLimit"];
    // 模型别名
    string model_alias_name = 19[json_name = "ModelAliasName"];
  }
}

message DescribeAppAgentListReq {
  string AppBizId = 1; // 应用ID, 必须指定
  EnvType EnvType = 2 ; // 环境标识
}

message DescribeAppAgentListRsp {
  string StaringAgentId = 1; // 入口启动AgentID
  repeated Agent  Agents = 2; // 应用Agent信息列表
  message Agent {
    string AgentId = 1; // AgentID，全局唯一索引
    string WorkflowId = 2; // WorkflowID，非空则当前Agent从workflow转换而来

    string Name = 3; // Agent名称，同一个应用内，Agent名称不能重复
    string IconUrl = 4; // 插件图标url

    string Instructions = 5; // Agent指令；当该Agent被调用时，将作为“系统提示词”使用，描述Agent应执行的操作和响应方式
    string HandoffDescription = 6; // 当Agent作为转交目标时的描述，用于让其他Agent的LLM理解其功能和转交时机
    repeated string Handoffs = 7;    // Agent可转交的子AgentId列表
    AgentModelInfo Model = 8; // Agent调用LLM时使用的模型配置

    repeated AgentToolInfo Tools = 9; // Agent可使用的工具列表
    repeated AgentPluginInfo Plugins = 10; // Agent可使用的工具列表
    bool IsStartingAgent = 11; // 当前Agent是否是启动Agent

    AgentTypeEnum AgentType = 12;   // Agent类型
  }

  // Agent的插件信息（前端用）
  message AgentPluginInfo {
    string PluginId = 1; // 插件ID
    repeated AgentPluginHeader Headers = 2; // 应用配置的插件header信息
    AgentModelInfo Model = 3; // 插件调用LLM时使用的模型配置，一般用于指定知识库问答插件的生成模型

    // 以下为定制插件专用
    PluginInfoTypeEnum PluginInfoType = 10;   // 插件信息类型
    AgentKnowledgeQAPlugin  KnowledgeQa = 11; // 知识库问答插件配置 when PluginType = PLUGIN_TYPE_KNOWLEDGE_QA

    enum PluginInfoTypeEnum {
      PLUGIN_INFO_TYPE_UNSPECIFIED = 0;  // 保留字段，兼容旧版本
      PLUGIN_INFO_TYPE_KNOWLEDGE_QA = 1; // 知识库问答插件
    }
  }

  message AgentModelInfo {
    // 对话历史条数限制
    uint32 history_limit = 6[json_name = "HistoryLimit"];
    // 模型名称 (trpc 接口使用)
    string model_name = 8[json_name = "ModelName"];
    // 模型是否开启
    bool is_enabled = 10[json_name = "IsEnabled"];
    // 温度
    float temperature = 14[json_name = "Temperature"];
    // TopP
    float top_p = 15[json_name = "TopP"];
    // 模型上下文长度字符限制
    string model_context_words_limit = 17[json_name = "ModelContextWordsLimit"];
    // 指令长度字符限制
    uint32 instructions_words_limit = 18[json_name = "InstructionsWordsLimit"];
    // 模型别名
    string model_alias_name = 19[json_name = "ModelAliasName"];
  }
}

message DescribeAgentHasReferencedPluginToolListReq {
  string PluginId = 1; // 插件ID
  string ToolId = 2; // 工具ID
}

message DescribeAgentHasReferencedPluginToolListRsp {
  repeated AgentToolRef ToolRefs = 1; // 工具引用信息
}

message AgentToolRef {
  string AppBizId = 1; // 应用id
  string ToolId = 2; // 工具id
  string PluginId = 3; // 插件id
}

// 获取应用Agent发布列表请求
message ListAppAgentReleasePreviewReq {
  uint64          BotBizId = 1 [(trpc.go_tag) = 'valid:"required~请传入正确的应用ID"']; // 机器人
  string          Query = 2; // 查询关键字, 用于模糊匹配标题
  uint64          StartTime = 3; // 任务更新时间起点, 时间单位 unix 秒
  uint64          EndTime = 4; // 任务更新时间止点, 时间单位 unix 秒
  repeated uint32 Actions = 5; // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
  uint32          PageNumber = 6; // 页码
  uint32          PageSize = 7 [(trpc.go_tag) = 'valid:"required,range(1|200)~每页数量在1到200之间"']; // 每页数量
  uint64          ReleaseBizId = 8; // 发布任务 ID
  repeated uint32 ReleaseStatus = 9; // 发布状态(2 待发布 3 发布中 4 已发布 5 发布失败)
}

// 获取应用Agent发布列表返回
message ListAppAgentReleasePreviewRsp {

  // AppAgent 发布列表详情
  message AppAgent {
    string        AgentId = 1; // 工具ID
    string        AgentName = 2; // 工具名称
    uint64        UpdateTime = 3; // 更新时间, unix 秒时间戳 (s)
    uint32        Action = 4; // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
    string        ActionDesc = 5; // 状态, 状态值：1:新增, 2:修改, 3:删除, 4:发布
    string        Message = 6; // 发布消息
  }
  uint32            Total = 1; // 总数
  repeated AppAgent List = 2; // 列表
}

message ClearAgentAppResourceReq {
  // 应用业务ID
  string AppBizId = 1;
  // 任务ID
  uint64 TaskId = 2;
}

message ClearAgentAppResourceRsp {}

// Agent的插件信息（前端用）
message AgentPluginInfo {
  string PluginId = 1; // 插件ID
  repeated AgentPluginHeader Headers = 2; // 应用配置的插件header信息
  AgentModelInfo Model = 3; // 插件调用LLM时使用的模型配置，一般用于指定知识库问答插件的生成模型

  // 以下为定制插件专用
  PluginInfoTypeEnum PluginInfoType = 10;   // 插件类型
  AgentKnowledgeQAPlugin  KnowledgeQa = 11; // 知识库问答插件配置 when PluginType = PLUGIN_TYPE_KNOWLEDGE_QA

  enum PluginInfoTypeEnum {
    PLUGIN_INFO_TYPE_UNSPECIFIED = 0;  // 保留字段，兼容旧版本
    PLUGIN_INFO_TYPE_KNOWLEDGE_QA = 1; // 知识库问答插件
  }
}

// Agent的工具信息（前端用）
message AgentToolInfo {
  string PluginId = 1; // 插件ID
  string PluginName = 2; // 插件名称
  string IconUrl = 3; // 插件图标url
  trpc.KEP.plugin_config_server.PluginTypeEnum PluginType = 4; // 插件类型
  string ToolId = 5; // 工具id
  string ToolName = 6; // 工具名称
  string ToolDesc = 7; // 工具描述信息
  repeated AgentToolReqParam Inputs = 8; // 输入参数
  repeated AgentToolRspParam Outputs = 9; // 输出参数
  trpc.KEP.plugin_config_server.CreateTypeEnum CreateType = 10; // 创建方式 0-服务 1-代码 2-MCP
  AgentMCPServerInfo McpServer = 11; // MCP插件的配置信息

  bool IsBindingKnowledge = 12; //该工具是否和知识库绑定
  int32 Status = 13; //插件状态 1-成功(可用)，2-不可用
  bool IsStreamReply = 14; // 是否是流式回复
  repeated AgentPluginHeader Headers = 20; // 应用配置的插件header信息
}

// MCP插件的配置信息, 因为云API不支持map类型，所以这里用repeated来表示
// borrowed from trpc.KEP.plugin_config_server.MCPServerInfo
message AgentMCPServerInfo {
  string McpServerUrl = 1; // mcp server地址
  repeated AgentPluginHeader Headers = 2; // mcp server header信息
  int32 Timeout = 3; // 超时时间，单位秒
  int32 SseReadTimeout = 4; // sse服务超时时间，单位秒
}

// Agent配置MCP插件header信息
message AgentPluginHeader {
  string ParamName = 1; // 参数名称
  string ParamValue = 2; // 参数值, Input未指定时，使用该值，兼容旧版本
  bool GlobalHidden = 3; // header参数配置是否隐藏不可见，true-隐藏不可见，false-可见
  bool IsRequired = 4; // 是否必选
  Input Input = 8;    // 输入的值
}

// 应用工具的请求参数定义
message AgentToolReqParam {
  string Name = 1;  // 参数名称
  string Desc = 2;  // 参数描述
  trpc.KEP.plugin_config_server.TypeEnum Type = 3;// 参数类型
  bool IsRequired = 4; // 是否必选
  string DefaultValue = 5; // 默认值, Input未指定时，使用该值，兼容旧版本
  repeated AgentToolReqParam SubParams = 6;  // 子参数,ParamType 是OBJECT 或 ARRAY<>类型有用
  bool AgentHidden = 7;  //agent模式下模型是否可见
  repeated AgentToolReqParam OneOf = 8;  // OneOf类型的参数
  repeated AgentToolReqParam AnyOf = 9; // AnyOf类型的参数
  Input Input = 10;    // 输入的值
}

// 应用工具的响应参数定义
message AgentToolRspParam {
  string Name = 1; // 参数名称
  string Desc = 2; // 变量描述
  trpc.KEP.plugin_config_server.TypeEnum Type = 3; // 参数类型
  repeated AgentToolRspParam SubParams = 4; // 只对 OBJECT 或 ARRAY_OBJECT 类型有用
  bool AgentHidden = 5;  //agent模式下模型是否可见
  bool IsIncrementReply = 6; // 工具如果是流式回复，该字段是否是增量回复
}

// ------------------------------------------------ 基础结构体定义 start  ------------------------------------------------
// borrowed from https://git.woa.com/dialogue-platform/lke_proto/blob/master/thirds-pb/bot-task-config-server/workflow.proto#L282
// 输入值，支持直接赋值和引用
message Input {
  InputSourceEnum InputType = 1;       // 输入来源类型

  UserInputValue UserInputValue = 2;   // 用户手写输入
  string CustomVarId = 4;              // 自定义变量（API参数）
}

// 输入值来源类型
enum InputSourceEnum {
  UNSPECIFIED = 0;        // 保留字段，兼容旧版本
  USER_INPUT = 1;         // 用户输入
  CUSTOM_VARIABLE = 3;    // 自定义变量（API参数）
}

// 用户手写输入
message UserInputValue {
  repeated string Values = 1; // 用户输入的值
  // DynamicValue Value = 2; // 未来扩展
}

// 系统参数
message SystemVariable {
  string Name = 1;                // 系统参数名
  int32 DialogHistoryLimit = 2;   // 对话历史轮数的配置；如果Input是系统变量中的“对话历史”时才使用；
}

enum AgentTypeEnum {
  AGENT_TYPE_UNSPECIFIED = 0;// 保留字段，兼容旧版本
  AGENT_TYPE_KB_AGENT = 1;   // 知识库检索Agent
}

// 知识库问答插件
// borrowed from https://git.woa.com/dialogue-platform/lke_proto/blob/master/thirds-pb/bot-task-config-server/workflow.proto#L377
message AgentKnowledgeQAPlugin {
  KnowledgeFilter Filter = 1; // 知识检索筛选范围
}

message KnowledgeFilter{
  KnowledgeFilterEnum FilterType = 1;       // 知识检索筛选方式: [全部知识]、[文档和问答]、 [标签]

  KnowledgeFilterDocAndAnswer DocAndAnswer = 2; // 文档和问答过滤器
  KnowledgeFilterTag Tag = 3; // 标签过滤器
}

enum KnowledgeFilterEnum {
  KNOWLEDGE_FILTER_TYPE_ALL = 0;          // 全部知识
  KNOWLEDGE_FILTER_TYPE_DOC_AND_QA = 1;   // 按文档和问答
  KNOWLEDGE_FILTER_TYPE_TAG = 2;          // 按标签
}

message KnowledgeFilterDocAndAnswer{
  repeated string DocBizIds = 5;    // 文档ID列表
  bool AllQa = 6;                   // 问答
}

// 知识检索范围组 --  TAG 时生效
message KnowledgeFilterTag {
  enum OperatorEnum {
    AND = 0; // AND
    OR = 1;  // OR
  }
  OperatorEnum Operator = 1;                // 标签之间的关系，可以是 AND OR
  repeated KnowledgeAttrLabel  Labels = 2;  // 标签

  message KnowledgeAttrLabel{
    uint64 AttributeBizId = 1;  // 属性ID
    repeated Input Inputs = 2;  // 标签值，标签值之间是或的关系，只有匹配的，才会进行知识检索，否则报检索不到
  }
}
// ------------------------------------------------ 基础结构体定义 end  ------------------------------------------------

message AgentMigrateReq {
  int32 StartRobotId = 1; // 从哪个RobotId 开始迁移, 为0则从第一个开始
  int32 MigrateLimit = 2; // 迁移的数量限制, 为0则不限制
  repeated string AppIds = 3; //  如果指定了 appid 列表，直接上面参数无效，迁移指定的列表
}

message AgentMigrateRsp {
  int32 LastRobotId = 1;  // 迁移的最后一个 RobotId
}