syntax = "proto3";

package trpc.KEP.bot_data_statistics_server;
option go_package = "git.woa.com/dialogue-platform/lke_proto/pb-protocol/bot_data_statistics_server";

import "validate.proto";

// Api接口
service Api {
  // Demo
  rpc Demo(DemoReq) returns (DemoRsp);

  rpc CreateHistoryTask(CreateHistoryTaskReq) returns (CreateHistoryTaskRsp);
}

message DemoReq {}

message DemoRsp {}

message CreateHistoryTaskReq {
  string task_type = 1 ; // 任务类型
  string start_time = 2; // 起始时间
  string end_time = 3 ; // 结束时间
  bool remove_exist_task = 4 ;
}

message CreateHistoryTaskRsp {
  string msg = 1;
}