// Code generated by trpc-go/trpc-go-cmdline v2.7.0. DO NOT EDIT.
// source: bot-data-statistics.proto

package bot_data_statistics_server

import (
	"context"
	"errors"
	"fmt"

	_ "git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	_ "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/server"
)

// START ======================================= Server Service Definition ======================================= START

// ApiService defines service.
type ApiService interface {
	// Demo Demo
	Demo(ctx context.Context, req *DemoReq) (*DemoRsp, error)

	CreateHistoryTask(ctx context.Context, req *CreateHistoryTaskReq) (*CreateHistoryTaskRsp, error)
}

func ApiService_Demo_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &DemoReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).Demo(ctx, reqbody.(*DemoReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

func ApiService_CreateHistoryTask_Handler(svr interface{}, ctx context.Context, f server.FilterFunc) (interface{}, error) {
	req := &CreateHistoryTaskReq{}
	filters, err := f(req)
	if err != nil {
		return nil, err
	}
	handleFunc := func(ctx context.Context, reqbody interface{}) (interface{}, error) {
		return svr.(ApiService).CreateHistoryTask(ctx, reqbody.(*CreateHistoryTaskReq))
	}

	var rsp interface{}
	rsp, err = filters.Filter(ctx, req, handleFunc)
	if err != nil {
		return nil, err
	}
	return rsp, nil
}

// ApiServer_ServiceDesc descriptor for server.RegisterService.
var ApiServer_ServiceDesc = server.ServiceDesc{
	ServiceName: "trpc.KEP.bot_data_statistics_server.Api",
	HandlerType: ((*ApiService)(nil)),
	Methods: []server.Method{
		{
			Name: "/trpc.KEP.bot_data_statistics_server.Api/Demo",
			Func: ApiService_Demo_Handler,
		},
		{
			Name: "/trpc.KEP.bot_data_statistics_server.Api/CreateHistoryTask",
			Func: ApiService_CreateHistoryTask_Handler,
		},
	},
}

// RegisterApiService registers service.
func RegisterApiService(s server.Service, svr ApiService) {
	if err := s.Register(&ApiServer_ServiceDesc, svr); err != nil {
		panic(fmt.Sprintf("Api register error:%v", err))
	}
}

// START --------------------------------- Default Unimplemented Server Service --------------------------------- START

type UnimplementedApi struct{}

// Demo Demo
func (s *UnimplementedApi) Demo(ctx context.Context, req *DemoReq) (*DemoRsp, error) {
	return nil, errors.New("rpc Demo of service Api is not implemented")
}
func (s *UnimplementedApi) CreateHistoryTask(ctx context.Context, req *CreateHistoryTaskReq) (*CreateHistoryTaskRsp, error) {
	return nil, errors.New("rpc CreateHistoryTask of service Api is not implemented")
}

// END --------------------------------- Default Unimplemented Server Service --------------------------------- END

// END ======================================= Server Service Definition ======================================= END

// START ======================================= Client Service Definition ======================================= START

// ApiClientProxy defines service client proxy
type ApiClientProxy interface {
	// Demo Demo
	Demo(ctx context.Context, req *DemoReq, opts ...client.Option) (rsp *DemoRsp, err error)

	CreateHistoryTask(ctx context.Context, req *CreateHistoryTaskReq, opts ...client.Option) (rsp *CreateHistoryTaskRsp, err error)
}

type ApiClientProxyImpl struct {
	client client.Client
	opts   []client.Option
}

var NewApiClientProxy = func(opts ...client.Option) ApiClientProxy {
	return &ApiClientProxyImpl{client: client.DefaultClient, opts: opts}
}

func (c *ApiClientProxyImpl) Demo(ctx context.Context, req *DemoReq, opts ...client.Option) (*DemoRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_data_statistics_server.Api/Demo")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_data_statistics_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("Demo")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &DemoRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

func (c *ApiClientProxyImpl) CreateHistoryTask(ctx context.Context, req *CreateHistoryTaskReq, opts ...client.Option) (*CreateHistoryTaskRsp, error) {
	ctx, msg := codec.WithCloneMessage(ctx)
	defer codec.PutBackMessage(msg)
	msg.WithClientRPCName("/trpc.KEP.bot_data_statistics_server.Api/CreateHistoryTask")
	msg.WithCalleeServiceName(ApiServer_ServiceDesc.ServiceName)
	msg.WithCalleeApp("KEP")
	msg.WithCalleeServer("bot_data_statistics_server")
	msg.WithCalleeService("Api")
	msg.WithCalleeMethod("CreateHistoryTask")
	msg.WithSerializationType(codec.SerializationTypePB)
	callopts := make([]client.Option, 0, len(c.opts)+len(opts))
	callopts = append(callopts, c.opts...)
	callopts = append(callopts, opts...)
	rsp := &CreateHistoryTaskRsp{}
	if err := c.client.Invoke(ctx, req, rsp, callopts...); err != nil {
		return nil, err
	}
	return rsp, nil
}

// END ======================================= Client Service Definition ======================================= END
