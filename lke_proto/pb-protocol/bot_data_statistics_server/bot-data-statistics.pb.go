// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0
// 	protoc        v3.19.1
// source: bot-data-statistics.proto

package bot_data_statistics_server

import (
	reflect "reflect"
	sync "sync"

	_ "git.code.oa.com/devsec/protoc-gen-secv/validate"
	proto "github.com/golang/protobuf/proto"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// This is a compile-time assertion that a sufficiently up-to-date version
// of the legacy proto package is being used.
const _ = proto.ProtoPackageIsVersion4

type DemoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DemoReq) Reset() {
	*x = DemoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_data_statistics_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DemoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DemoReq) ProtoMessage() {}

func (x *DemoReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_data_statistics_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DemoReq.ProtoReflect.Descriptor instead.
func (*DemoReq) Descriptor() ([]byte, []int) {
	return file_bot_data_statistics_proto_rawDescGZIP(), []int{0}
}

type DemoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DemoRsp) Reset() {
	*x = DemoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_data_statistics_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DemoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DemoRsp) ProtoMessage() {}

func (x *DemoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_data_statistics_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DemoRsp.ProtoReflect.Descriptor instead.
func (*DemoRsp) Descriptor() ([]byte, []int) {
	return file_bot_data_statistics_proto_rawDescGZIP(), []int{1}
}

type CreateHistoryTaskReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskType        string `protobuf:"bytes,1,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`    // 任务类型
	StartTime       string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"` // 起始时间
	EndTime         string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`       // 结束时间
	RemoveExistTask bool   `protobuf:"varint,4,opt,name=remove_exist_task,json=removeExistTask,proto3" json:"remove_exist_task,omitempty"`
}

func (x *CreateHistoryTaskReq) Reset() {
	*x = CreateHistoryTaskReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_data_statistics_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateHistoryTaskReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHistoryTaskReq) ProtoMessage() {}

func (x *CreateHistoryTaskReq) ProtoReflect() protoreflect.Message {
	mi := &file_bot_data_statistics_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHistoryTaskReq.ProtoReflect.Descriptor instead.
func (*CreateHistoryTaskReq) Descriptor() ([]byte, []int) {
	return file_bot_data_statistics_proto_rawDescGZIP(), []int{2}
}

func (x *CreateHistoryTaskReq) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *CreateHistoryTaskReq) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *CreateHistoryTaskReq) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *CreateHistoryTaskReq) GetRemoveExistTask() bool {
	if x != nil {
		return x.RemoveExistTask
	}
	return false
}

type CreateHistoryTaskRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Msg string `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
}

func (x *CreateHistoryTaskRsp) Reset() {
	*x = CreateHistoryTaskRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bot_data_statistics_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateHistoryTaskRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateHistoryTaskRsp) ProtoMessage() {}

func (x *CreateHistoryTaskRsp) ProtoReflect() protoreflect.Message {
	mi := &file_bot_data_statistics_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateHistoryTaskRsp.ProtoReflect.Descriptor instead.
func (*CreateHistoryTaskRsp) Descriptor() ([]byte, []int) {
	return file_bot_data_statistics_proto_rawDescGZIP(), []int{3}
}

func (x *CreateHistoryTaskRsp) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

var File_bot_data_statistics_proto protoreflect.FileDescriptor

var file_bot_data_statistics_proto_rawDesc = []byte{
	0x0a, 0x19, 0x62, 0x6f, 0x74, 0x2d, 0x64, 0x61, 0x74, 0x61, 0x2d, 0x73, 0x74, 0x61, 0x74, 0x69,
	0x73, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x23, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x1a, 0x0e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x09, 0x0a, 0x07, 0x44, 0x65, 0x6d, 0x6f, 0x52, 0x65, 0x71, 0x22, 0x09, 0x0a, 0x07, 0x44,
	0x65, 0x6d, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x99, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x12,
	0x1b, 0x0a, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x5f, 0x65, 0x78, 0x69, 0x73, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0f, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x45, 0x78, 0x69, 0x73, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x22, 0x28, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x69, 0x73, 0x74,
	0x6f, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x32, 0xf5, 0x01, 0x0a,
	0x03, 0x41, 0x70, 0x69, 0x12, 0x62, 0x0a, 0x04, 0x44, 0x65, 0x6d, 0x6f, 0x12, 0x2c, 0x2e, 0x74,
	0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x44, 0x65, 0x6d, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x65, 0x6d, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x89, 0x01, 0x0a, 0x11, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x39,
	0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f,
	0x72, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x39, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x62, 0x6f, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x48, 0x69, 0x73, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x73, 0x70, 0x42, 0x50, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61,
	0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x2f, 0x62, 0x6f, 0x74, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x73, 0x74, 0x69, 0x63, 0x73, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_bot_data_statistics_proto_rawDescOnce sync.Once
	file_bot_data_statistics_proto_rawDescData = file_bot_data_statistics_proto_rawDesc
)

func file_bot_data_statistics_proto_rawDescGZIP() []byte {
	file_bot_data_statistics_proto_rawDescOnce.Do(func() {
		file_bot_data_statistics_proto_rawDescData = protoimpl.X.CompressGZIP(file_bot_data_statistics_proto_rawDescData)
	})
	return file_bot_data_statistics_proto_rawDescData
}

var file_bot_data_statistics_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_bot_data_statistics_proto_goTypes = []interface{}{
	(*DemoReq)(nil),              // 0: trpc.KEP.bot_data_statistics_server.DemoReq
	(*DemoRsp)(nil),              // 1: trpc.KEP.bot_data_statistics_server.DemoRsp
	(*CreateHistoryTaskReq)(nil), // 2: trpc.KEP.bot_data_statistics_server.CreateHistoryTaskReq
	(*CreateHistoryTaskRsp)(nil), // 3: trpc.KEP.bot_data_statistics_server.CreateHistoryTaskRsp
}
var file_bot_data_statistics_proto_depIdxs = []int32{
	0, // 0: trpc.KEP.bot_data_statistics_server.Api.Demo:input_type -> trpc.KEP.bot_data_statistics_server.DemoReq
	2, // 1: trpc.KEP.bot_data_statistics_server.Api.CreateHistoryTask:input_type -> trpc.KEP.bot_data_statistics_server.CreateHistoryTaskReq
	1, // 2: trpc.KEP.bot_data_statistics_server.Api.Demo:output_type -> trpc.KEP.bot_data_statistics_server.DemoRsp
	3, // 3: trpc.KEP.bot_data_statistics_server.Api.CreateHistoryTask:output_type -> trpc.KEP.bot_data_statistics_server.CreateHistoryTaskRsp
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_bot_data_statistics_proto_init() }
func file_bot_data_statistics_proto_init() {
	if File_bot_data_statistics_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bot_data_statistics_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DemoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_data_statistics_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DemoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_data_statistics_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateHistoryTaskReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_bot_data_statistics_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateHistoryTaskRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bot_data_statistics_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bot_data_statistics_proto_goTypes,
		DependencyIndexes: file_bot_data_statistics_proto_depIdxs,
		MessageInfos:      file_bot_data_statistics_proto_msgTypes,
	}.Build()
	File_bot_data_statistics_proto = out.File
	file_bot_data_statistics_proto_rawDesc = nil
	file_bot_data_statistics_proto_goTypes = nil
	file_bot_data_statistics_proto_depIdxs = nil
}
