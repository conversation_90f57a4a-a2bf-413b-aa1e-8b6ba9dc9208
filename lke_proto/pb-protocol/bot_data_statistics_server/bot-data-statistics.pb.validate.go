// Code generated by protoc-gen-secv. DO NOT EDIT.
// source: bot-data-statistics.proto

package bot_data_statistics_server

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
	_ = unicode.IsUpper
	_ = json.Valid([]byte(""))
)

// Validate checks the field values on DemoReq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DemoReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DemoReq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DemoReqMultiError, or nil if none found.
func (m *DemoReq) ValidateAll() error {
	return m.validate(true)
}

func (m *DemoReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DemoReqMultiError(errors)
	}

	return nil
}

// DemoReqMultiError is an error wrapping multiple validation errors returned
// by DemoReq.ValidateAll() if the designated constraints aren't met.
type DemoReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DemoReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DemoReqMultiError) AllErrors() []error { return m }

// DemoReqValidationError is the validation error returned by DemoReq.Validate
// if the designated constraints aren't met.
type DemoReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DemoReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DemoReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DemoReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DemoReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DemoReqValidationError) ErrorName() string { return "DemoReqValidationError" }

// Error satisfies the builtin error interface
func (e DemoReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDemoReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DemoReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DemoReqValidationError{}

// Validate checks the field values on DemoRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DemoRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DemoRsp with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DemoRspMultiError, or nil if none found.
func (m *DemoRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *DemoRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DemoRspMultiError(errors)
	}

	return nil
}

// DemoRspMultiError is an error wrapping multiple validation errors returned
// by DemoRsp.ValidateAll() if the designated constraints aren't met.
type DemoRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DemoRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DemoRspMultiError) AllErrors() []error { return m }

// DemoRspValidationError is the validation error returned by DemoRsp.Validate
// if the designated constraints aren't met.
type DemoRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DemoRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DemoRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DemoRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DemoRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DemoRspValidationError) ErrorName() string { return "DemoRspValidationError" }

// Error satisfies the builtin error interface
func (e DemoRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDemoRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DemoRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DemoRspValidationError{}

// Validate checks the field values on CreateHistoryTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateHistoryTaskReq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateHistoryTaskReq with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateHistoryTaskReqMultiError, or nil if none found.
func (m *CreateHistoryTaskReq) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHistoryTaskReq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TaskType

	// no validation rules for StartTime

	// no validation rules for EndTime

	// no validation rules for RemoveExistTask

	if len(errors) > 0 {
		return CreateHistoryTaskReqMultiError(errors)
	}

	return nil
}

// CreateHistoryTaskReqMultiError is an error wrapping multiple validation
// errors returned by CreateHistoryTaskReq.ValidateAll() if the designated
// constraints aren't met.
type CreateHistoryTaskReqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHistoryTaskReqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHistoryTaskReqMultiError) AllErrors() []error { return m }

// CreateHistoryTaskReqValidationError is the validation error returned by
// CreateHistoryTaskReq.Validate if the designated constraints aren't met.
type CreateHistoryTaskReqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHistoryTaskReqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateHistoryTaskReqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateHistoryTaskReqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateHistoryTaskReqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHistoryTaskReqValidationError) ErrorName() string {
	return "CreateHistoryTaskReqValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHistoryTaskReqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHistoryTaskReq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHistoryTaskReqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHistoryTaskReqValidationError{}

// Validate checks the field values on CreateHistoryTaskRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateHistoryTaskRsp) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateHistoryTaskRsp with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateHistoryTaskRspMultiError, or nil if none found.
func (m *CreateHistoryTaskRsp) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateHistoryTaskRsp) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Msg

	if len(errors) > 0 {
		return CreateHistoryTaskRspMultiError(errors)
	}

	return nil
}

// CreateHistoryTaskRspMultiError is an error wrapping multiple validation
// errors returned by CreateHistoryTaskRsp.ValidateAll() if the designated
// constraints aren't met.
type CreateHistoryTaskRspMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateHistoryTaskRspMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateHistoryTaskRspMultiError) AllErrors() []error { return m }

// CreateHistoryTaskRspValidationError is the validation error returned by
// CreateHistoryTaskRsp.Validate if the designated constraints aren't met.
type CreateHistoryTaskRspValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateHistoryTaskRspValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateHistoryTaskRspValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateHistoryTaskRspValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateHistoryTaskRspValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateHistoryTaskRspValidationError) ErrorName() string {
	return "CreateHistoryTaskRspValidationError"
}

// Error satisfies the builtin error interface
func (e CreateHistoryTaskRspValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateHistoryTaskRsp.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateHistoryTaskRspValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateHistoryTaskRspValidationError{}
