// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.29.3
// source: schedule-server.proto

package resource_schedule_server

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Server 类型
type ServerTypeEnum int32

const (
	ServerTypeEnum_UNKNOWN     ServerTypeEnum = 0 // UNKNOWN
	ServerTypeEnum_SANDBOX_MCP ServerTypeEnum = 1 // 沙箱MCP
	ServerTypeEnum_SANDBOX_VNC ServerTypeEnum = 2 // 沙箱VNC
)

// Enum value maps for ServerTypeEnum.
var (
	ServerTypeEnum_name = map[int32]string{
		0: "UNKNOWN",
		1: "SANDBOX_MCP",
		2: "SANDBOX_VNC",
	}
	ServerTypeEnum_value = map[string]int32{
		"UNKNOWN":     0,
		"SANDBOX_MCP": 1,
		"SANDBOX_VNC": 2,
	}
)

func (x ServerTypeEnum) Enum() *ServerTypeEnum {
	p := new(ServerTypeEnum)
	*p = x
	return p
}

func (x ServerTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServerTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_schedule_server_proto_enumTypes[0].Descriptor()
}

func (ServerTypeEnum) Type() protoreflect.EnumType {
	return &file_schedule_server_proto_enumTypes[0]
}

func (x ServerTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServerTypeEnum.Descriptor instead.
func (ServerTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{0}
}

type EchoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EchoReq) Reset() {
	*x = EchoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoReq) ProtoMessage() {}

func (x *EchoReq) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoReq.ProtoReflect.Descriptor instead.
func (*EchoReq) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{0}
}

type EchoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EchoRsp) Reset() {
	*x = EchoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EchoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EchoRsp) ProtoMessage() {}

func (x *EchoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EchoRsp.ProtoReflect.Descriptor instead.
func (*EchoRsp) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{1}
}

// Pod 信息
type PodInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IP         string                `protobuf:"bytes,1,opt,name=IP,proto3" json:"IP,omitempty"`                  // Pod IP
	IsFirstUse bool                  `protobuf:"varint,2,opt,name=IsFirstUse,proto3" json:"IsFirstUse,omitempty"` // 是否首次使用
	Servers    []*PodInfo_ServerInfo `protobuf:"bytes,3,rep,name=Servers,proto3" json:"Servers,omitempty"`        // Pod servers
}

func (x *PodInfo) Reset() {
	*x = PodInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodInfo) ProtoMessage() {}

func (x *PodInfo) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodInfo.ProtoReflect.Descriptor instead.
func (*PodInfo) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{2}
}

func (x *PodInfo) GetIP() string {
	if x != nil {
		return x.IP
	}
	return ""
}

func (x *PodInfo) GetIsFirstUse() bool {
	if x != nil {
		return x.IsFirstUse
	}
	return false
}

func (x *PodInfo) GetServers() []*PodInfo_ServerInfo {
	if x != nil {
		return x.Servers
	}
	return nil
}

// 调度pod资源请求
type SchedulePodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin       string `protobuf:"bytes,1,opt,name=Uin,proto3" json:"Uin,omitempty"`             // Uin
	AppBizId  string `protobuf:"bytes,2,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`   // 应用ID
	SessionId string `protobuf:"bytes,3,opt,name=SessionId,proto3" json:"SessionId,omitempty"` // SessionID
}

func (x *SchedulePodReq) Reset() {
	*x = SchedulePodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SchedulePodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchedulePodReq) ProtoMessage() {}

func (x *SchedulePodReq) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchedulePodReq.ProtoReflect.Descriptor instead.
func (*SchedulePodReq) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{3}
}

func (x *SchedulePodReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *SchedulePodReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *SchedulePodReq) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

// 调度pod资源返回
type SchedulePodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PodInfo *PodInfo `protobuf:"bytes,1,opt,name=PodInfo,proto3" json:"PodInfo,omitempty"` // Pod 信息
}

func (x *SchedulePodRsp) Reset() {
	*x = SchedulePodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SchedulePodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchedulePodRsp) ProtoMessage() {}

func (x *SchedulePodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchedulePodRsp.ProtoReflect.Descriptor instead.
func (*SchedulePodRsp) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{4}
}

func (x *SchedulePodRsp) GetPodInfo() *PodInfo {
	if x != nil {
		return x.PodInfo
	}
	return nil
}

// 释放pod资源请求
type CancelPodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin       string `protobuf:"bytes,1,opt,name=Uin,proto3" json:"Uin,omitempty"`             // Uin
	AppBizId  string `protobuf:"bytes,2,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`   // 应用ID
	SessionId string `protobuf:"bytes,3,opt,name=SessionId,proto3" json:"SessionId,omitempty"` // SessionID
}

func (x *CancelPodReq) Reset() {
	*x = CancelPodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelPodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPodReq) ProtoMessage() {}

func (x *CancelPodReq) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPodReq.ProtoReflect.Descriptor instead.
func (*CancelPodReq) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{5}
}

func (x *CancelPodReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *CancelPodReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *CancelPodReq) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

// 释放pod资源返回
type CancelPodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success bool   `protobuf:"varint,1,opt,name=Success,proto3" json:"Success,omitempty"` // 是否成功
	Message string `protobuf:"bytes,2,opt,name=Message,proto3" json:"Message,omitempty"`  // 执行信息
}

func (x *CancelPodRsp) Reset() {
	*x = CancelPodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelPodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPodRsp) ProtoMessage() {}

func (x *CancelPodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPodRsp.ProtoReflect.Descriptor instead.
func (*CancelPodRsp) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{6}
}

func (x *CancelPodRsp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CancelPodRsp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取已调度的pod资源请求
type GetScheduledPodReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uin       string `protobuf:"bytes,1,opt,name=Uin,proto3" json:"Uin,omitempty"`             // Uin
	AppBizId  string `protobuf:"bytes,2,opt,name=AppBizId,proto3" json:"AppBizId,omitempty"`   // 应用ID
	SessionId string `protobuf:"bytes,3,opt,name=SessionId,proto3" json:"SessionId,omitempty"` // SessionID
}

func (x *GetScheduledPodReq) Reset() {
	*x = GetScheduledPodReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScheduledPodReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScheduledPodReq) ProtoMessage() {}

func (x *GetScheduledPodReq) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScheduledPodReq.ProtoReflect.Descriptor instead.
func (*GetScheduledPodReq) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{7}
}

func (x *GetScheduledPodReq) GetUin() string {
	if x != nil {
		return x.Uin
	}
	return ""
}

func (x *GetScheduledPodReq) GetAppBizId() string {
	if x != nil {
		return x.AppBizId
	}
	return ""
}

func (x *GetScheduledPodReq) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

// 获取已调度的pod资源返回
type GetScheduledPodRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PodInfo *PodInfo `protobuf:"bytes,1,opt,name=PodInfo,proto3" json:"PodInfo,omitempty"` // Pod 信息
}

func (x *GetScheduledPodRsp) Reset() {
	*x = GetScheduledPodRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetScheduledPodRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetScheduledPodRsp) ProtoMessage() {}

func (x *GetScheduledPodRsp) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetScheduledPodRsp.ProtoReflect.Descriptor instead.
func (*GetScheduledPodRsp) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{8}
}

func (x *GetScheduledPodRsp) GetPodInfo() *PodInfo {
	if x != nil {
		return x.PodInfo
	}
	return nil
}

type PodInfo_ServerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type ServerTypeEnum `protobuf:"varint,1,opt,name=Type,proto3,enum=trpc.KEP.resource_schedule_server.ServerTypeEnum" json:"Type,omitempty"` // Server 类型
	Port int32          `protobuf:"varint,2,opt,name=Port,proto3" json:"Port,omitempty"`                                                       // Server Port
	URL  string         `protobuf:"bytes,3,opt,name=URL,proto3" json:"URL,omitempty"`                                                          // Server URL eg: http://IP:Port/runtime_mcp/sse
}

func (x *PodInfo_ServerInfo) Reset() {
	*x = PodInfo_ServerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_schedule_server_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PodInfo_ServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodInfo_ServerInfo) ProtoMessage() {}

func (x *PodInfo_ServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_schedule_server_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodInfo_ServerInfo.ProtoReflect.Descriptor instead.
func (*PodInfo_ServerInfo) Descriptor() ([]byte, []int) {
	return file_schedule_server_proto_rawDescGZIP(), []int{2, 0}
}

func (x *PodInfo_ServerInfo) GetType() ServerTypeEnum {
	if x != nil {
		return x.Type
	}
	return ServerTypeEnum_UNKNOWN
}

func (x *PodInfo_ServerInfo) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *PodInfo_ServerInfo) GetURL() string {
	if x != nil {
		return x.URL
	}
	return ""
}

var File_schedule_server_proto protoreflect.FileDescriptor

var file_schedule_server_proto_rawDesc = []byte{
	0x0a, 0x15, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45,
	0x50, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x0a, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x09, 0x0a, 0x07, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x65,
	0x71, 0x22, 0x09, 0x0a, 0x07, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x85, 0x02, 0x0a,
	0x07, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x50, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x50, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x55, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x49, 0x73,
	0x46, 0x69, 0x72, 0x73, 0x74, 0x55, 0x73, 0x65, 0x12, 0x4f, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63,
	0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f,
	0x64, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x1a, 0x79, 0x0a, 0x0a, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x50, 0x6f,
	0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x52, 0x4c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x55, 0x52, 0x4c, 0x22, 0x5c, 0x0a, 0x0e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42,
	0x69, 0x7a, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x22, 0x56, 0x0a, 0x0e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x6f,
	0x64, 0x52, 0x73, 0x70, 0x12, 0x44, 0x0a, 0x07, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x07, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x5a, 0x0a, 0x0c, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x69,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x53, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x42, 0x0a, 0x0c, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c,
	0x50, 0x6f, 0x64, 0x52, 0x73, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x60, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71,
	0x12, 0x10, 0x0a, 0x03, 0x55, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55,
	0x69, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41, 0x70, 0x70, 0x42, 0x69, 0x7a, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x5a, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x50, 0x6f, 0x64, 0x52,
	0x73, 0x70, 0x12, 0x44, 0x0a, 0x07, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x07, 0x50, 0x6f, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x2a, 0x3f, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e,
	0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x41, 0x4e, 0x44, 0x42,
	0x4f, 0x58, 0x5f, 0x4d, 0x43, 0x50, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x41, 0x4e, 0x44,
	0x42, 0x4f, 0x58, 0x5f, 0x56, 0x4e, 0x43, 0x10, 0x02, 0x32, 0xde, 0x03, 0x0a, 0x0e, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x60, 0x0a, 0x04,
	0x45, 0x63, 0x68, 0x6f, 0x12, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x65, 0x71,
	0x1a, 0x2a, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x45, 0x63, 0x68, 0x6f, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x75,
	0x0a, 0x0b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x6f, 0x64, 0x12, 0x31, 0x2e,
	0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x6f, 0x64, 0x52, 0x65, 0x71,
	0x1a, 0x31, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x50, 0x6f, 0x64,
	0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x09, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50,
	0x6f, 0x64, 0x12, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x6f, 0x64,
	0x52, 0x65, 0x71, 0x1a, 0x2f, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x50, 0x6f,
	0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x50, 0x6f, 0x64, 0x12, 0x35, 0x2e, 0x74, 0x72, 0x70,
	0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x64, 0x50, 0x6f, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x35, 0x2e, 0x74, 0x72, 0x70, 0x63, 0x2e, 0x4b, 0x45, 0x50, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x64, 0x50, 0x6f, 0x64, 0x52, 0x73, 0x70, 0x22, 0x00, 0x42, 0x4e, 0x5a, 0x4c, 0x67, 0x69,
	0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x64, 0x69, 0x61, 0x6c, 0x6f, 0x67,
	0x75, 0x65, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x6c, 0x6b, 0x65, 0x5f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x70, 0x62, 0x2d, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_schedule_server_proto_rawDescOnce sync.Once
	file_schedule_server_proto_rawDescData = file_schedule_server_proto_rawDesc
)

func file_schedule_server_proto_rawDescGZIP() []byte {
	file_schedule_server_proto_rawDescOnce.Do(func() {
		file_schedule_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_schedule_server_proto_rawDescData)
	})
	return file_schedule_server_proto_rawDescData
}

var file_schedule_server_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_schedule_server_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_schedule_server_proto_goTypes = []any{
	(ServerTypeEnum)(0),        // 0: trpc.KEP.resource_schedule_server.ServerTypeEnum
	(*EchoReq)(nil),            // 1: trpc.KEP.resource_schedule_server.EchoReq
	(*EchoRsp)(nil),            // 2: trpc.KEP.resource_schedule_server.EchoRsp
	(*PodInfo)(nil),            // 3: trpc.KEP.resource_schedule_server.PodInfo
	(*SchedulePodReq)(nil),     // 4: trpc.KEP.resource_schedule_server.SchedulePodReq
	(*SchedulePodRsp)(nil),     // 5: trpc.KEP.resource_schedule_server.SchedulePodRsp
	(*CancelPodReq)(nil),       // 6: trpc.KEP.resource_schedule_server.CancelPodReq
	(*CancelPodRsp)(nil),       // 7: trpc.KEP.resource_schedule_server.CancelPodRsp
	(*GetScheduledPodReq)(nil), // 8: trpc.KEP.resource_schedule_server.GetScheduledPodReq
	(*GetScheduledPodRsp)(nil), // 9: trpc.KEP.resource_schedule_server.GetScheduledPodRsp
	(*PodInfo_ServerInfo)(nil), // 10: trpc.KEP.resource_schedule_server.PodInfo.ServerInfo
}
var file_schedule_server_proto_depIdxs = []int32{
	10, // 0: trpc.KEP.resource_schedule_server.PodInfo.Servers:type_name -> trpc.KEP.resource_schedule_server.PodInfo.ServerInfo
	3,  // 1: trpc.KEP.resource_schedule_server.SchedulePodRsp.PodInfo:type_name -> trpc.KEP.resource_schedule_server.PodInfo
	3,  // 2: trpc.KEP.resource_schedule_server.GetScheduledPodRsp.PodInfo:type_name -> trpc.KEP.resource_schedule_server.PodInfo
	0,  // 3: trpc.KEP.resource_schedule_server.PodInfo.ServerInfo.Type:type_name -> trpc.KEP.resource_schedule_server.ServerTypeEnum
	1,  // 4: trpc.KEP.resource_schedule_server.ScheduleServer.Echo:input_type -> trpc.KEP.resource_schedule_server.EchoReq
	4,  // 5: trpc.KEP.resource_schedule_server.ScheduleServer.SchedulePod:input_type -> trpc.KEP.resource_schedule_server.SchedulePodReq
	6,  // 6: trpc.KEP.resource_schedule_server.ScheduleServer.CancelPod:input_type -> trpc.KEP.resource_schedule_server.CancelPodReq
	8,  // 7: trpc.KEP.resource_schedule_server.ScheduleServer.GetScheduledPod:input_type -> trpc.KEP.resource_schedule_server.GetScheduledPodReq
	2,  // 8: trpc.KEP.resource_schedule_server.ScheduleServer.Echo:output_type -> trpc.KEP.resource_schedule_server.EchoRsp
	5,  // 9: trpc.KEP.resource_schedule_server.ScheduleServer.SchedulePod:output_type -> trpc.KEP.resource_schedule_server.SchedulePodRsp
	7,  // 10: trpc.KEP.resource_schedule_server.ScheduleServer.CancelPod:output_type -> trpc.KEP.resource_schedule_server.CancelPodRsp
	9,  // 11: trpc.KEP.resource_schedule_server.ScheduleServer.GetScheduledPod:output_type -> trpc.KEP.resource_schedule_server.GetScheduledPodRsp
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_schedule_server_proto_init() }
func file_schedule_server_proto_init() {
	if File_schedule_server_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_schedule_server_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*EchoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_schedule_server_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*EchoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_schedule_server_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*PodInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_schedule_server_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*SchedulePodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_schedule_server_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*SchedulePodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_schedule_server_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*CancelPodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_schedule_server_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*CancelPodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_schedule_server_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*GetScheduledPodReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_schedule_server_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*GetScheduledPodRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_schedule_server_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*PodInfo_ServerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_schedule_server_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_schedule_server_proto_goTypes,
		DependencyIndexes: file_schedule_server_proto_depIdxs,
		EnumInfos:         file_schedule_server_proto_enumTypes,
		MessageInfos:      file_schedule_server_proto_msgTypes,
	}.Build()
	File_schedule_server_proto = out.File
	file_schedule_server_proto_rawDesc = nil
	file_schedule_server_proto_goTypes = nil
	file_schedule_server_proto_depIdxs = nil
}
